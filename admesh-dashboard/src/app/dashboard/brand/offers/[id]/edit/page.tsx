"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { auth } from "@/lib/firebase";
import { dollarsToCents, centsToDollars } from "@/lib/utils";
import {
  handleTestConversion,
  checkTestConversions,
} from "@/lib/conversion-utils";
import { ArrowLeft, Loader2, Save, Trash2, Info } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import PixelImplementationExamples from "@/components/PixelImplementationExamples";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import OfferIncentiveForm from "@/app/dashboard/brand/onboarding/components/ui/OfferIncentiveForm";
import { OfferIncentive } from "@/types/onboarding";

interface OfferData {
  title?: string;
  description?: string;
  url?: string;
  reward_note?: string;
  offer_total_budget_allocated?: number;
  categories?: string[];
  keywords?: string[];
  payout?: {
    amount?: number;
    currency?: string;
    model?: string;
  };

  tracking?: {
    method?: string;
    webhook_url?: string;
    notes?: string;
    redirect_url?: string;
    target_urls?: string[];
    updated_at?: { seconds: number; nanoseconds: number } | null;
  };
  // The redirect_url might be at the root level in some API responses
  redirect_url?: string;
  suggestion_reason?: string;
  active?: boolean; // Backward compatibility
  offer_status?: "active" | "inactive" | "cancelled"; // Primary status field
  status?: string; // Backward compatibility
  goal?: string;
  product_id?: string;
  product_name?: string;
  trust_score?: number;
  offer_views?: {
    total: number;
    test: number;
    production: number;
  };

  // Wallet information
  wallet?: {
    total_available_balance?: number;
    total_promo_available_balance?: number;
    total_promo_balance_spent?: number;
    total_balance_spent?: number;
    total_budget_allocated?: number;
    brand_id?: string;
  };

  // Promo information
  promo_applied?: boolean;
  offer_total_promo_available?: number;
  offer_total_promo_spent?: number;
  offer_intial_promo_balance?: number;
  promo_conversions_left?: number;

  // Offer incentive
  offer_incentive?: OfferIncentive;

  // Marketing content fields
  offer_title?: string;
  offer_description?: string;
  feature_sections?: Array<{
    title: string;
    description: string;
    icon: string;
  }>;
  offer_images?: string[];

  // Click and conversion tracking
  click_count?: {
    total: number;
    test: number;
    production: number;
  };
  conversion_count?: {
    total: number;
    test: number;
    production: number;
  };

  // Spending tracking
  total_spent?: {
    all: number;
    test: number;
    production: number;
  };
  offer_total_budget_spent?: number;

  // Additional fields from database
  brand_id?: string;
  offer_id?: string;
  created_at?: { seconds: number; nanoseconds: number } | string;
}

interface FormData {
  url: string;
  reward_note: string;
  budget: string;
  categories: string;
  keywords: string;
  payout_amount: string;
  payout_currency: string;
  payout_model: string;
  goal: string;
  redirect_url: string;
  webhook_url: string;
  tracking_notes: string;
  tracking_method: string;
  target_urls: string[];
  pixel_url: string;
  click_url: string;
  suggestion_reason: string;
  active: boolean; // Backward compatibility
  offer_status: "active" | "inactive" | "cancelled"; // Primary status field
  // Marketing content fields
  offer_title: string;
  offer_description: string;
  // Additional fields
  promo_applied: boolean;
  promo_conversions_left: number;
}

export default function EditOfferPage() {
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [unsavedChangesDialogOpen, setUnsavedChangesDialogOpen] =
    useState(false);
  const [testConversionDialogOpen, setTestConversionDialogOpen] =
    useState(false);
  const [testConversionCount, setTestConversionCount] = useState(0);
  const [isTestingConversion, setIsTestingConversion] = useState(false);
  const [isCheckingConversions, setIsCheckingConversions] = useState(false);
  const [testConversionExists, setTestConversionExists] = useState(false);
  const [showHowItWorks, setShowHowItWorks] = useState(false);
  const [originalOffer, setOriginalOffer] = useState<OfferData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [promoAmount, setPromoAmount] = useState<number>(0);
  const [promoApplied, setPromoApplied] = useState<boolean>(false);
  const [offerIncentive, setOfferIncentive] = useState<OfferIncentive | undefined>(undefined);

  // Form validation errors
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
    url?: string;
    budget?: string;
    payout_amount?: string;
    redirect_url?: string;
    target_urls?: string[];
  }>({});
  // Remove unused implementation status tracking
  /*const [implementationStatus, setImplementationStatus] = useState({
    pixelAdded: false,
    conversionTested: false,
    conversionVerified: false
  })*/

  const [form, setForm] = useState<FormData>({
    url: "",
    reward_note: "",
    budget: "",
    categories: "",
    keywords: "",
    payout_amount: "",
    payout_currency: "USD",
    payout_model: "CPA",
    goal: "signup",
    redirect_url: "",
    webhook_url: "",
    tracking_notes: "",
    tracking_method: "redirect_pixel", // Default tracking method
    target_urls: [], // Array of target URLs
    pixel_url: "", // Tracking pixel URL
    click_url: "", // Click URL for server-side API
    suggestion_reason: "",
    active: true, // Backward compatibility
    offer_status: "active" as "active" | "inactive" | "cancelled", // Primary status field
    // Marketing content fields
    offer_title: "",
    offer_description: "",
    // Additional fields
    promo_applied: false,
    promo_conversions_left: 0,
  });

  const [codeLanguage, setCodeLanguage] = useState("nodejs");

  useEffect(() => {
    // Load test conversion count from localStorage
    const storedCount = localStorage.getItem(`test_conversion_count_${id}`);
    if (storedCount) {
      setTestConversionCount(parseInt(storedCount, 10));
    }
  }, [id]);

  // Save test conversion count to localStorage whenever it changes
  useEffect(() => {
    if (testConversionCount > 0) {
      localStorage.setItem(
        `test_conversion_count_${id}`,
        testConversionCount.toString()
      );
    }
  }, [testConversionCount, id]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get the Firebase auth token for API calls
        const token = await auth.currentUser?.getIdToken();
        if (!token) {
          toast.error("Authentication required");
          setLoading(false);
          return;
        }

        // Fetch the specific offer by ID
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';
        console.log("Using API base URL for fetch:", apiBaseUrl);

        const response = await fetch(
          `${apiBaseUrl}/offers/get/${id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!response.ok) {
          if (response.status === 404) {
            toast.error("Offer not found");
            router.push("/dashboard/brand/offers");
            return;
          }
          throw new Error("Failed to fetch offer");
        }

        const offer = await response.json();
        console.log("Offer data from API:", offer);

        const data = offer;
        setOriginalOffer(data);

        // Debug: Log the tracking data to see what we're receiving
        console.log("Offer data:", data);
        console.log("Tracking data:", data.tracking);
        console.log("Redirect URL:", data.tracking?.redirect_url);

        // Prepare form data
        const formData = {
          url: data.url || "",
          reward_note: data.reward_note || "", // Will be auto-calculated later
          // Convert budget from cents to dollars for display
          budget: data.offer_total_budget_allocated ? centsToDollars(data.offer_total_budget_allocated).toString() : "",
          categories: (data.categories || []).join(", "),
          keywords: (data.keywords || []).join(", "),
          // Convert payout amount from cents to dollars for display
          payout_amount: data.payout?.amount
            ? centsToDollars(data.payout.amount).toString()
            : "",
          payout_currency: data.payout?.currency || "USD",
          payout_model: data.payout?.model || "CPA",
          goal: data.goal || "signup",
          redirect_url: data.tracking?.redirect_url || data.redirect_url || "",
          webhook_url: data.tracking?.webhook_url || "",
          tracking_notes: data.tracking?.notes || "",
          tracking_method: data.tracking?.method || "redirect_pixel",
          target_urls: data.tracking?.target_urls || [],
          pixel_url: `<img src="https://api.admesh.io/pixel?ad_id=${id}" width="1" height="1" style="display:none" />`,
          click_url: `https://api.admesh.io/conversion/log`,
          suggestion_reason: data.suggestion_reason || "",
          active: !!data.active || (data.offer_status === "active"), // Backward compatibility
          offer_status: data.offer_status || data.status || (data.active ? "active" : "inactive"), // Primary status field
          // Marketing content fields
          offer_title: data.offer_title || "",
          offer_description: data.offer_description || "",
          // Additional fields
          promo_applied: data.promo_applied || false,
          promo_conversions_left: data.promo_conversions_left || 0,
        };

        // Calculate the reward note based on payout amount, goal, and currency
        const currencySymbol =
          formData.payout_currency === "EUR"
            ? "€"
            : formData.payout_currency === "GBP"
            ? "£"
            : "$";
        formData.reward_note = `${currencySymbol}${formData.payout_amount} per ${formData.goal}`;

        // Debug: Log the form data before setting it
        console.log("Form data redirect_url:", formData.redirect_url);

        // Set the form data
        setForm(formData);

        // Store the original offer data for change detection
        setOriginalOffer(data);

        // Get wallet balance from the offer data
        if (data.wallet) {
          // Calculate remaining wallet balance that can be used for offers
          const totalAvailableBalance = data.wallet.total_available_balance || 0;
          const totalBudgetAllocated = data.wallet.total_budget_allocated || 0;

          // If this is an existing offer, we need to exclude its current budget from the calculation
          // to avoid counting it twice in the allocated amount
          let currentOfferBudget = 0;
          if (data.offer_total_budget_allocated) {
            currentOfferBudget = data.offer_total_budget_allocated;
            console.log("Current offer budget:", currentOfferBudget);
          }

          // Add the current offer's budget back to the available balance
          const adjustedTotalBudgetAllocated = Math.max(0, totalBudgetAllocated - currentOfferBudget);
          const remainingBalance = Math.max(0, totalAvailableBalance - adjustedTotalBudgetAllocated);

          // We no longer need to set wallet balance as a state variable
          // since we're calculating max budget directly in the validation
          console.log("Wallet data:", data.wallet);
          console.log("Total available balance:", totalAvailableBalance);
          console.log("Total budget allocated:", totalBudgetAllocated);
          console.log("Adjusted budget allocated:", adjustedTotalBudgetAllocated);
          console.log("Remaining wallet balance:", remainingBalance);
        }

        // Set promo amount and promo applied status
        setPromoApplied(!!data.promo_applied);
        setPromoAmount(data.offer_total_promo_available || 0);
        console.log("Promo applied:", data.promo_applied);
        console.log("Promo amount:", data.offer_total_promo_available);

        // Set offer incentive if it exists
        if (data.offer_incentive) {
          setOfferIncentive(data.offer_incentive);
        }
      } catch (err) {
        console.error("Error fetching offer:", err);
        toast.error("Failed to load offer details");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchData();
    }
  }, [id, router]);

  // Add a beforeunload event listener to warn when navigating away with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasChanges) {
        // Standard way to show a confirmation dialog before leaving the page
        e.preventDefault();
        // Note: returnValue is deprecated but still required for browser compatibility
        e.returnValue = "";
        return "";
      }
    };

    // For browser navigation
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasChanges]);

  useEffect(() => {
    if (originalOffer) {
      // Check if any form values have changed
      const hasAnyChanges =
        form.offer_title !== originalOffer.offer_title ||
        form.offer_description !== originalOffer.offer_description ||
        form.url !== originalOffer.url ||
        form.reward_note !== originalOffer.reward_note ||
        form.budget !== (originalOffer.offer_total_budget_allocated ? centsToDollars(originalOffer.offer_total_budget_allocated).toString() : "") ||
        form.categories !== (originalOffer.categories || []).join(", ") ||
        form.keywords !== (originalOffer.keywords || []).join(", ") ||
        form.payout_amount !== originalOffer.payout?.amount?.toString() ||
        form.payout_currency !== originalOffer.payout?.currency ||
        form.payout_model !== originalOffer.payout?.model ||
        form.goal !== originalOffer.goal ||
        form.redirect_url !==
          (originalOffer.tracking?.redirect_url ||
            originalOffer.redirect_url) ||
        form.webhook_url !== originalOffer.tracking?.webhook_url ||
        form.tracking_notes !== originalOffer.tracking?.notes ||
        form.tracking_method !== originalOffer.tracking?.method ||
        JSON.stringify(form.target_urls) !==
          JSON.stringify(originalOffer.tracking?.target_urls || []) ||
        form.suggestion_reason !== originalOffer.suggestion_reason ||
        form.offer_status !== (originalOffer.offer_status || originalOffer.status || (originalOffer.active ? "active" : "inactive")) ||
        JSON.stringify(offerIncentive) !== JSON.stringify(originalOffer.offer_incentive);

      setHasChanges(hasAnyChanges);
    }
  }, [form, originalOffer, offerIncentive]);

  // Helper function to get currency symbol
  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case "EUR":
        return "€";
      case "GBP":
        return "£";
      default:
        return "$";
    }
  };

  // Helper function to update reward note
  const updateRewardNote = (amount: string, goal: string, currency: string) => {
    const currencySymbol = getCurrencySymbol(currency);
    return `${currencySymbol}${amount} per ${goal}`;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    // Create a copy of the current errors
    const newErrors = { ...errors };

    // Validate based on field name
    switch (name) {
      case "title":
        if (!value.trim()) {
          newErrors.title = "Title is required";
        } else if (value.length > 100) {
          newErrors.title = "Title must be less than 100 characters";
        } else {
          delete newErrors.title;
        }
        break;

      case "description":
        if (value.length > 500) {
          newErrors.description = "Description must be less than 500 characters";
        } else {
          delete newErrors.description;
        }
        break;

      case "url":
        if (!value.trim()) {
          newErrors.url = "URL is required";
        } else if (!/^https?:\/\/.+/.test(value)) {
          newErrors.url = "URL must start with http:// or https://";
        } else {
          delete newErrors.url;
        }
        break;

      case "budget":
        const budgetValue = parseFloat(value);
        const budgetInCents = dollarsToCents(value);
        // Calculate the maximum allowed budget (total_available_balance + original offer budget)
        const maxBudget = (originalOffer?.wallet?.total_available_balance ?? 0) + (originalOffer?.offer_total_budget_allocated ?? 0);

        if (!value.trim()) {
          newErrors.budget = "Budget is required";
        } else if (isNaN(budgetValue) || budgetValue <= 0) {
          newErrors.budget = "Budget must be a positive number";
        } else if (budgetInCents > maxBudget) {
          // Budget cannot exceed total available balance + original offer budget
          newErrors.budget = `Budget cannot exceed available wallet balance: $${centsToDollars(maxBudget)}`;
        } else if (promoApplied && budgetInCents < promoAmount) {
          // Show a more prominent error when budget is less than promo amount
          newErrors.budget = `ERROR: Budget ($${value}) is less than promo amount ($${centsToDollars(promoAmount)}).
          When using promo credit, your budget must be greater than or equal to the promo amount.`;
        } else {
          delete newErrors.budget;
        }
        break;

      case "payout_amount":
        const payoutValue = parseFloat(value);

        if (!value.trim()) {
          newErrors.payout_amount = "Payout amount is required";
        } else if (isNaN(payoutValue) || payoutValue <= 0) {
          newErrors.payout_amount = "Payout amount must be a positive number";
        } else if (payoutValue < 10) {
          newErrors.payout_amount = "Minimum payout amount is $10.00";
        } else {
          delete newErrors.payout_amount;
        }
        break;

      case "redirect_url":
        if (form.tracking_method === "redirect_pixel" && !value.trim()) {
          newErrors.redirect_url = "Redirect URL is required for this tracking method";
        } else if (value.trim() && !/^https?:\/\/.+/.test(value)) {
          newErrors.redirect_url = "URL must start with http:// or https://";
        } else {
          delete newErrors.redirect_url;
        }
        break;
    }

    // Update the errors state
    setErrors(newErrors);

    setForm((prev) => {
      // If changing the payout amount, update the reward note
      if (name === "payout_amount") {
        return {
          ...prev,
          [name]: value,
          reward_note: updateRewardNote(value, prev.goal, prev.payout_currency),
        };
      }
      return { ...prev, [name]: value };
    });
  };

  const handleSelectChange = (name: string, value: string) => {
    setForm((prev) => {
      // If changing the goal, update the reward note
      if (name === "goal") {
        return {
          ...prev,
          [name]: value,
          reward_note: updateRewardNote(
            prev.payout_amount,
            value,
            prev.payout_currency
          ),
        };
      }
      // If changing the currency, update the reward note
      if (name === "payout_currency") {
        return {
          ...prev,
          [name]: value,
          reward_note: updateRewardNote(prev.payout_amount, prev.goal, value),
        };
      }
      return { ...prev, [name]: value };
    });
  };

  const payoutModelDescriptions = {
    CPA: "Pay when a specific action is completed (e.g., signup, purchase)",
    CPC: "Pay for each click on your affiliate links",
    CPL: "Pay when a qualified lead is generated",
    CPS: "Pay when a sale is completed",
    RevShare: "Share a percentage of revenue with affiliates",
  };

  const trackingMethodOptions = [
    {
      value: "redirect_pixel",
      label: "Redirect + Pixel",
      description:
        "Easiest setup. We'll provide a redirect URL and tracking pixel for your thank-you page.",
      bestFor: "Most websites with a thank you/confirmation page",
      setupTime: "5-10 minutes",
    },
    // {
    //   value: "server_api",
    //   label: "Server-Side API",
    //   description:
    //     "Most secure. Your server will send conversion data to our API when a conversion happens.",
    //   bestFor: "SaaS products, apps with backend integration capabilities",
    //   setupTime: "15-30 minutes",
    // },
    // {
    //   value: "manual",
    //   label: "Manual Approval",
    //   description: "You'll manually approve conversions in your dashboard. Best for custom campaigns.",
    //   bestFor: "Low-volume campaigns or unique conversion flows",
    //   setupTime: "No setup required"
    // }
  ];

  const validateForm = () => {
    const newErrors = { ...errors };
    let isValid = true;

    // Validate offer title
    if (!form.offer_title.trim()) {
      newErrors.title = "Offer title is required";
      isValid = false;
    } else if (form.offer_title.length > 100) {
      newErrors.title = "Offer title must be less than 100 characters";
      isValid = false;
    } else {
      delete newErrors.title;
    }

    // Validate offer description (optional but with length limit)
    if (form.offer_description.length > 500) {
      newErrors.description = "Offer description must be less than 500 characters";
      isValid = false;
    } else {
      delete newErrors.description;
    }

    // Validate URL
    if (!form.url.trim()) {
      newErrors.url = "URL is required";
      isValid = false;
    } else if (!/^https?:\/\/.+/.test(form.url)) {
      newErrors.url = "URL must start with http:// or https://";
      isValid = false;
    } else {
      delete newErrors.url;
    }

    // Validate budget
    const budgetValue = parseFloat(form.budget);
    const budgetInCents = dollarsToCents(form.budget);
    // Calculate the maximum allowed budget (total_available_balance + original offer budget)
    const maxBudget = (originalOffer?.wallet?.total_available_balance ?? 0) + (originalOffer?.offer_total_budget_allocated ?? 0);

    if (!form.budget.trim()) {
      newErrors.budget = "Budget is required";
      isValid = false;
    } else if (isNaN(budgetValue) || budgetValue <= 0) {
      newErrors.budget = "Budget must be a positive number";
      isValid = false;
    } else if (budgetInCents > maxBudget) {
      // Budget cannot exceed total available balance + original offer budget
      newErrors.budget = `Budget cannot exceed available wallet balance: $${centsToDollars(maxBudget)}`;
      isValid = false;
    } else if (promoApplied && budgetInCents < promoAmount) {
      // Show a more prominent error when budget is less than promo amount
      newErrors.budget = `ERROR: Budget ($${form.budget}) is less than promo amount ($${centsToDollars(promoAmount)}).
      When using promo credit, your budget must be greater than or equal to the promo amount.`;
      isValid = false;
    } else {
      delete newErrors.budget;
    }

    // Validate payout amount
    const payoutValue = parseFloat(form.payout_amount);

    if (!form.payout_amount.trim()) {
      newErrors.payout_amount = "Payout amount is required";
      isValid = false;
    } else if (isNaN(payoutValue) || payoutValue <= 0) {
      newErrors.payout_amount = "Payout amount must be a positive number";
      isValid = false;
    } else if (payoutValue < 10) {
      newErrors.payout_amount = "Minimum payout amount is $10.00";
      isValid = false;
    } else {
      delete newErrors.payout_amount;
    }

    // Validate redirect URL if using redirect_pixel tracking method
    if (form.tracking_method === "redirect_pixel") {
      if (!form.redirect_url.trim()) {
        newErrors.redirect_url = "Redirect URL is required for this tracking method";
        isValid = false;
      } else if (!/^https?:\/\/.+/.test(form.redirect_url)) {
        newErrors.redirect_url = "URL must start with http:// or https://";
        isValid = false;
      } else {
        delete newErrors.redirect_url;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleUpdate = async () => {
    // Validate the form
    if (!validateForm()) {
      // Show error toast for the first error
      const firstError = Object.values(errors)[0];
      if (firstError) {
        toast.error(firstError);
      }
      return;
    }

    setSaving(true);
    try {
      // Get the Firebase auth token for API calls
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        toast.error("Authentication required");
        return;
      }

      // Prepare the offer data for the API
      const offerData = {
        url: form.url,
        reward_note: form.reward_note,
        // Convert budget from dollars to cents for storage
        budget: dollarsToCents(form.budget),
        categories: form.categories
          .split(",")
          .map((c) => c.trim())
          .filter(Boolean),
        keywords: form.keywords
          .split(",")
          .map((k) => k.trim())
          .filter(Boolean),
        goal: form.goal,
        payout: {
          // Convert payout amount from dollars to cents for storage
          amount: dollarsToCents(form.payout_amount),
          currency: form.payout_currency,
          model: form.payout_model,
        },
        suggestion_reason: form.suggestion_reason,
        active: form.active,
        // Include trust_score if it exists in the original offer
        ...(originalOffer?.trust_score !== undefined && {
          trust_score: originalOffer.trust_score
        }),
        // Include offer_views if it exists in the original offer
        ...(originalOffer?.offer_views !== undefined && {
          offer_views: originalOffer.offer_views
        }),
      };

      // Update the offer via API
      // First, update the offer details using the PATCH endpoint
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';
      console.log("Using API base URL:", apiBaseUrl);

      const offerResponse = await fetch(
        `${apiBaseUrl}/offers/${id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            url: offerData.url,
            reward_note: offerData.reward_note,
            offer_total_budget_allocated: offerData.budget,
            categories: offerData.categories,
            keywords: offerData.keywords,
            goal: offerData.goal,
            payout: offerData.payout,
            suggestion_reason: offerData.suggestion_reason,
            offer_status: form.offer_status, // Use the form's offer_status
            // Include trust_score if it exists in the original offer
            ...(offerData.trust_score !== undefined && {
              trust_score: offerData.trust_score
            }),
            // Include offer_views if it exists in the original offer
            ...(offerData.offer_views !== undefined && {
              offer_views: offerData.offer_views
            }),
            // Include offer incentive if provided
            offer_incentive: offerIncentive,
            // Include marketing content fields
            offer_title: form.offer_title,
            offer_description: form.offer_description,
            feature_sections: originalOffer?.feature_sections || [],
          }),
        }
      );

      if (!offerResponse.ok) {
        const errorData = await offerResponse.json();
        throw new Error(errorData.detail || "Failed to update offer");
      }

      // Debug: Log the tracking data being sent
      const trackingData = {
        offer_id: id,
        product_id: originalOffer?.product_id || "",
        method: form.tracking_method,
        redirect_url: form.redirect_url,
        webhook_url: form.webhook_url,
        notes: form.tracking_notes,
        target_urls: form.target_urls,
      };

      console.log("Sending tracking data:", trackingData);

      // Then, update the tracking settings
      const trackingResponse = await fetch(
        `${apiBaseUrl}/offers/tracking/setup`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(trackingData),
        }
      );

      if (!trackingResponse.ok) {
        const trackingErrorData = await trackingResponse.json();
        console.error("Error setting up tracking:", trackingErrorData);
        toast.warning("Offer updated, but tracking setup failed.");
      } else {
        // Debug: Log the successful tracking response
        const trackingResponseData = await trackingResponse.json();
        console.log("Tracking setup successful:", trackingResponseData);
      }

      toast.success("Offer updated successfully");
      router.push("/dashboard/brand/offers");
    } catch (err) {
      toast.error("Failed to update offer");
      console.error("Error updating offer:", err);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      setUnsavedChangesDialogOpen(true);
    } else {
      router.back();
    }
  };

  const handleDiscardChanges = () => {
    setUnsavedChangesDialogOpen(false);
    router.back();
  };

  // Function to handle test conversion
  const handleTestConversionClick = async () => {
    await handleTestConversion({
      offerId: id,
      redirectUrl: form.redirect_url,
      user: auth.currentUser,
      productId: originalOffer?.product_id || "",
      setIsTestingConversion,
    });
  };

  // Function to check for test conversions
  const checkTestConversionsClick = async () => {
    await checkTestConversions({
      offerId: id,
      user: auth.currentUser,
      setIsCheckingConversions,
      setTestConversionExists,
      setTestConversionCount,
    });
  };

  const handleDelete = async () => {
    try {
      setDeleting(true);

      // Get the Firebase auth token
      const token = await auth.currentUser?.getIdToken();
      if (!token) {
        toast.error("Authentication required");
        return;
      }

      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.useadmesh.com';
      console.log("Using API base URL for delete:", apiBaseUrl);

      const response = await fetch(
        `${apiBaseUrl}/offers/${id}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to delete offer");
      }

      toast.success("Offer deleted successfully");
      router.push("/dashboard/brand/offers");
    } catch (error) {
      console.error("Error deleting offer:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to delete offer"
      );
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-8 px-4 space-y-6">
      <div className="flex items-center justify-between mb-2">
        <div
          className="flex items-center gap-2 cursor-pointer text-muted-foreground hover:text-foreground transition"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="text-sm font-medium">Back to Offers</span>
        </div>

      
      </div>

      <Card className="border shadow-sm">
        <CardHeader className="bg-slate-50 border-b pb-4">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="text-2xl">Edit Offer</CardTitle>
              <CardDescription className="mt-1">
                Update your offer details and tracking methods
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="active-status"
                  checked={form.offer_status === "active"}
                  onCheckedChange={(val) =>
                    setForm((p) => ({
                      ...p,
                      offer_status: val ? "active" : (p.offer_status === "cancelled" ? "cancelled" : "inactive"),
                      active: val // Keep backward compatibility
                    }))
                  }
                />
                <Label htmlFor="active-status">
                  <Badge
                    variant={form.offer_status === "active" ? "default" : "secondary"}
                    className={
                      form.offer_status === "active"
                        ? "ml-1 bg-green-100 text-green-700 border-green-200"
                        : form.offer_status === "cancelled"
                        ? "ml-1 bg-red-100 text-red-700 border-red-200"
                        : "ml-1 bg-gray-100 text-gray-600 border-gray-200"
                    }
                  >
                    {form.offer_status === "active" ? "Active" : form.offer_status === "cancelled" ? "Cancelled" : "Inactive"}
                  </Badge>
                </Label>
              </div>

              <AlertDialog
                open={deleteDialogOpen}
                onOpenChange={setDeleteDialogOpen}
              >
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete this offer?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action cannot be undone. This will permanently remove
                      the offer from your account.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {deleting ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <Trash2 className="w-4 h-4 mr-1" />
                      )}
                      {deleting ? "Deleting..." : "Delete Offer"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardHeader>

        <Tabs defaultValue="details" className="w-full">
          <div className="px-6 pt-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger
                value="details"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Offer Details
              </TabsTrigger>
              <TabsTrigger
                value="tracking"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Tracking Setup
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="details" className="mt-0 p-0">
            <CardContent className="pt-6 space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label htmlFor="offer_title" className="text-sm font-medium">
                      Offer Title
                    </Label>
                    <button
                      type="button"
                      className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-800 dark:hover:bg-blue-700 text-blue-700 dark:text-blue-300 rounded-md transition-colors"
                      onClick={() => {
                        // TODO: Add regenerate title functionality
                        console.log('Regenerate title');
                      }}
                    >
                      Regenerate
                    </button>
                  </div>
                  <Input
                    id="offer_title"
                    name="offer_title"
                    value={form.offer_title}
                    onChange={handleChange}
                    className={`mt-1 ${errors.title ? 'border-red-500' : ''}`}
                    placeholder="e.g. Get $10 for signing up"
                    disabled={saving}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Optimized for AI agent recommendations ({form.offer_title.length}/60 chars)
                  </p>
                  {errors.title && (
                    <p className="text-xs text-red-500 mt-2 p-2 bg-red-50 border border-red-100 rounded-md">
                      {errors.title}
                    </p>
                  )}
                </div>

               
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label htmlFor="offer_description" className="text-sm font-medium">
                    Offer Description
                  </Label>
                  <button
                    type="button"
                    className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-800 dark:hover:bg-blue-700 text-blue-700 dark:text-blue-300 rounded-md transition-colors"
                    onClick={() => {
                      // TODO: Add regenerate description functionality
                      console.log('Regenerate description');
                    }}
                  >
                    Regenerate
                  </button>
                </div>
                <Textarea
                  id="offer_description"
                  name="offer_description"
                  value={form.offer_description}
                  onChange={handleChange}
                  className={`mt-1 ${errors.description ? 'border-red-500' : ''}`}
                  placeholder="Describe your offer in detail"
                  disabled={saving}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Used in ad formats ({form.offer_description.length}/150 chars)
                </p>
                {errors.description && (
                  <p className="text-xs text-red-500 mt-2 p-2 bg-red-50 border border-red-100 rounded-md">
                    {errors.description}
                  </p>
                )}
              </div>



              {/* Feature Sections - Editable */}
              {/* {originalOffer?.feature_sections && originalOffer.feature_sections.length > 0 && (
                <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-3">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Feature Sections ({originalOffer.feature_sections.length})
                    </label>
                    <button
                      type="button"
                      className="text-xs px-2 py-1 bg-blue-100 hover:bg-blue-200 dark:bg-blue-800 dark:hover:bg-blue-700 text-blue-700 dark:text-blue-300 rounded-md transition-colors"
                      onClick={() => {
                        // TODO: Add regenerate features functionality
                        console.log('Regenerate features');
                      }}
                    >
                      Regenerate All
                    </button>
                  </div>
                  <div className="space-y-4">
                    {originalOffer.feature_sections.map((feature, index) => (
                      <div key={index} className="bg-white dark:bg-gray-800 p-4 rounded-lg border">
                        <div className="grid gap-3 md:grid-cols-3">
                        
                          <div>
                            <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Title
                            </Label>
                            <Input
                              value={feature.title}
                              onChange={(e) => {
                                const updatedFeatures = [...(originalOffer.feature_sections || [])];
                                updatedFeatures[index] = { ...feature, title: e.target.value };
                                setOriginalOffer({ ...originalOffer, feature_sections: updatedFeatures });
                              }}
                              className="mt-1"
                              placeholder="Feature title"
                            />
                          </div>
                          <div>
                            <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">
                              Description
                            </Label>
                            <Textarea
                              value={feature.description}
                              onChange={(e) => {
                                const updatedFeatures = [...(originalOffer.feature_sections || [])];
                                updatedFeatures[index] = { ...feature, description: e.target.value };
                                setOriginalOffer({ ...originalOffer, feature_sections: updatedFeatures });
                              }}
                              className="mt-1"
                              placeholder="Feature description"
                              rows={2}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground mt-3">
                    These features are used in expandable ad formats and AI agent recommendations.
                  </p>
                </div>
              )} */}

              <Separator className="my-4" />

              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <Label
                    htmlFor="goal"
                    className="text-sm font-medium flex items-center gap-1"
                  >
                    Conversion Goal
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </Label>
                  <Select
                    value={form.goal}
                    onValueChange={(value) => handleSelectChange("goal", value)}
                    disabled={saving}
                  >
                    <SelectTrigger id="goal" className="mt-1">
                      <SelectValue placeholder="Select goal" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="signup">Signup</SelectItem>
                      {/* <SelectItem value="purchase">Purchase</SelectItem>
                      <SelectItem value="lead">Lead</SelectItem>
                      <SelectItem value="app_install">App Install</SelectItem>
                      <SelectItem value="click">Click</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    The action that triggers a payout
                  </p>
                </div>

                <div>
                  <Label
                    htmlFor="payout_model"
                    className="text-sm font-medium flex items-center gap-1"
                  >
                    Payout Model
                    <Info className="h-4 w-4 text-muted-foreground" />
                  </Label>
                  <Select
                    value={form.payout_model}
                    onValueChange={(value) =>
                      handleSelectChange("payout_model", value)
                    }
                    disabled={saving}
                  >
                    <SelectTrigger id="payout_model" className="mt-1">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CPA">
                        CPA - Cost Per Acquisition
                      </SelectItem>
                      {/* <SelectItem value="CPC">CPC - Cost Per Click</SelectItem> */}
                      {/* <SelectItem value="CPL">CPL - Cost Per Lead</SelectItem>
                    <SelectItem value="CPS">CPS - Cost Per Sale</SelectItem>
                    <SelectItem value="RevShare">Revenue Share</SelectItem> */}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    {payoutModelDescriptions[
                      form.payout_model as keyof typeof payoutModelDescriptions
                    ] || "How affiliates will be compensated"}
                  </p>
                </div>
              </div>

              <div className="grid gap-6 md:grid-cols-3">
                <div>
                  <Label
                    htmlFor="payout_amount"
                    className="text-sm font-medium"
                  >
                    Payout Amount
                  </Label>
                  <div className="relative mt-1">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="payout_amount"
                      name="payout_amount"
                      value={form.payout_amount}
                      onChange={handleChange}
                      placeholder="10.00"
                      type="number"
                      min="10"
                      step="0.01"
                      className={`pl-8 ${errors.payout_amount ? 'border-red-500' : ''}`}
                      disabled={saving}
                    />
                  </div>

                  {/* Always show the payout info */}
                  <p className="text-xs text-muted-foreground mt-1">
                    Amount paid per conversion (minimum $10.00)
                  </p>

                  {/* Show validation error if any */}
                  {errors.payout_amount && (
                    <p className="text-xs text-red-500 mt-2 p-2 bg-red-50 border border-red-100 rounded-md">
                      {errors.payout_amount}
                    </p>
                  )}
                </div>

                <div>
                  <Label
                    htmlFor="payout_currency"
                    className="text-sm font-medium"
                  >
                    Currency
                  </Label>
                  <Select
                    value={form.payout_currency}
                    onValueChange={(value) =>
                      handleSelectChange("payout_currency", value)
                    }
                    disabled={saving}
                  >
                    <SelectTrigger id="payout_currency" className="mt-1">
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD - US Dollar</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Currency for payouts
                  </p>
                </div>

                <div>
                  <Label htmlFor="budget" className="text-sm font-medium">
                    Total Budget
                  </Label>
                  <div className="relative mt-1">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      $
                    </span>
                    <Input
                      id="budget"
                      name="budget"
                      value={form.budget}
                      onChange={handleChange}
                      type="number"
                      min="0"
                      step="0.01"
                      className={`pl-8 ${errors.budget ? 'border-red-500' : ''}`}
                      placeholder="100.00"
                      disabled={saving}
                    />
                  </div>

                  {/* Always show the maximum spend info */}
                  <p className="text-xs text-muted-foreground mt-1">
                    Maximum spend for this campaign
                  </p>

                  {/* Show validation error if any */}
                  {errors.budget && (
                    <p className="text-xs text-red-500 mt-2 p-2 bg-red-50 border border-red-100 rounded-md">
                      {errors.budget}
                    </p>
                  )}
                </div>
              </div>



              {/* Offer Incentive Section */}
              <div>
                <OfferIncentiveForm
                  incentive={offerIncentive}
                  onChange={setOfferIncentive}
                />
              </div>

              <Separator className="my-4" />

             
            </CardContent>

            <CardFooter className="flex justify-between border-t p-6 bg-gray-50">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdate}
                disabled={saving || !hasChanges}
                className="px-8"
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </TabsContent>

          <TabsContent value="tracking" className="mt-0 p-0">
            <CardContent className="pt-6 space-y-6">
              <div>
                <Label className="text-sm font-medium">
                  Verification Method
                </Label>
                <div className="grid gap-4 mt-3">
                  {trackingMethodOptions.map((option) => (
                    <div
                      key={option.value}
                      className={`
              relative flex items-start p-4 rounded-lg border-2 cursor-pointer
              ${
                form.tracking_method === option.value
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              }
            `}
                      onClick={() =>
                        handleSelectChange("tracking_method", option.value)
                      }
                    >
                      <div className="min-w-0 flex-1 text-sm">
                        <div className="font-medium flex items-center">
                          <div
                            className={`w-4 h-4 mr-3 rounded-full flex items-center justify-center ${
                              form.tracking_method === option.value
                                ? "bg-primary"
                                : "border border-gray-300"
                            }`}
                          >
                            {form.tracking_method === option.value && (
                              <div className="w-2 h-2 rounded-full bg-white" />
                            )}
                          </div>
                          {option.label}
                        </div>
                        <p className="text-muted-foreground mt-1">
                          {option.description}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <div className="flex items-center">
                              <span className="font-medium mr-1">
                                Best for:
                              </span>{" "}
                              {option.bestFor}
                            </div>
                            <div className="flex items-center">
                              <span className="font-medium mr-1">
                                Setup time:
                              </span>{" "}
                              {option.setupTime}
                            </div>
                          </div>
                          <button
                            type="button"
                            className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowHowItWorks((prev) => !prev);
                            }}
                          >
                            How it works
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {showHowItWorks && (
                <div className="mt-4 bg-blue-50 p-4 rounded-lg border border-blue-200 animate-fadeIn">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium text-blue-800">
                      How It Works
                    </h4>
                    <button
                      type="button"
                      className="text-xs text-blue-600 hover:text-blue-800 hover:underline"
                      onClick={() => setShowHowItWorks(false)}
                    >
                      Close
                    </button>
                  </div>
                  {form.tracking_method === "redirect_pixel" ? (
                    <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                      <li>
                        Someone clicks on a promotional link for your product
                      </li>
                      <li>
                        They visit your website where you&apos;ve added our
                        simple tracking code
                      </li>
                      <li>
                        When they complete a purchase or sign up, we
                        automatically record this success
                      </li>
                      <li>
                        The promoter gets credit, and you see all results in
                        your dashboard
                      </li>
                    </ol>
                  ) : form.tracking_method === "server_api" ? (
                    <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                      <li>
                        Someone clicks on a promotional link for your product
                      </li>
                      <li>
                        When they make a purchase, your server sends this
                        information to our API
                      </li>
                      <li>
                        We verify the purchase and credit the promoter
                        automatically
                      </li>
                      <li>
                        You can see all conversions and payouts in your
                        dashboard
                      </li>
                    </ol>
                  ) : (
                    <ol className="list-decimal pl-5 text-sm space-y-2 text-blue-700">
                      <li>
                        Someone clicks on a promotional link for your product
                      </li>
                      <li>You&apos;ll see potential sales in your dashboard</li>
                      <li>
                        You simply approve or decline each sale as it happens
                      </li>
                      <li>
                        Approved conversions are credited to the promoter
                        automatically
                      </li>
                    </ol>
                  )}
                </div>
              )}

              <div className="mt-6 space-y-6">
                {form.tracking_method === "redirect_pixel" && (
                  <div className="space-y-6">
                    <div>
                      <Label
                        htmlFor="redirect_url"
                        className="text-sm font-medium"
                      >
                        Redirect URL
                      </Label>
                      <Input
                        id="redirect_url"
                        name="redirect_url"
                        value={form.redirect_url}
                        onChange={handleChange}
                        placeholder="https://yourbrand.com/signup"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Where users will be sent after clicking affiliate links
                      </p>
                    </div>

                    <div>
                      <Label
                        htmlFor="target_urls"
                        className="text-sm font-medium"
                      >
                        Target URLs (Optional)
                      </Label>
                      <div className="space-y-2 mt-1">
                        {form.target_urls.map((url, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Input
                              value={url}
                              onChange={(e) => {
                                const newUrls = [...form.target_urls];
                                newUrls[index] = e.target.value;
                                setForm((prev) => ({
                                  ...prev,
                                  target_urls: newUrls,
                                }));
                              }}
                              placeholder="https://yourbrand.com/target-page"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                const newUrls = [...form.target_urls];
                                newUrls.splice(index, 1);
                                setForm((prev) => ({
                                  ...prev,
                                  target_urls: newUrls,
                                }));
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setForm((prev) => ({
                              ...prev,
                              target_urls: [...prev.target_urls, ""],
                            }));
                          }}
                        >
                          Add Target URL
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Additional URLs that will be tracked for this offer
                        (e.g., specific product pages)
                      </p>
                    </div>

                    {/* Implementation Examples */}
                    <div className="mt-4">
                      <PixelImplementationExamples />
                    </div>
                    <div className="space-y-2">
                      <Label
                        htmlFor="pixel_code"
                        className="text-sm font-medium"
                      >
                        Your Tracking Pixel
                      </Label>

                      <div className="flex gap-2 mt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const emailBody = `Hi,

We need to implement AdMesh conversion tracking on our thank-you page. I've prepared detailed instructions below:

==============================================
ADMESH CONVERSION TRACKING IMPLEMENTATION GUIDE
==============================================

OVERVIEW:
---------
We need to add conversion tracking to our thank-you/confirmation page that appears after a user completes a ${
                              form.goal || "signup"
                            }.
This will allow us to track conversions from our affiliate partners.

IMPLEMENTATION STEPS:
--------------------
1. LOCATE THE THANK-YOU PAGE
   - Find the HTML file or component for our thank-you/confirmation page
   - This is the page that appears AFTER a successful ${form.goal || "signup"}

2. ADD THIS CODE BEFORE THE CLOSING </body> TAG:

<!-- AdMesh Conversion Tracking - Add before </body> tag -->
<script>
  // Get the click_id from URL parameters
  function getClickIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('click_id') || '';
  }

  // Check if this is an AdMesh referral
  function isAdMeshReferral() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('admesh') === 'true';
  }

  // Get the test parameter from URL
  function getTestParam() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('test') || 'false';
  }

  // Only fire the pixel if we have a click_id and this is an AdMesh referral
  const clickId = getClickIdFromUrl();
  const test = getTestParam();
  if (clickId && isAdMeshReferral()) {
    const img = document.createElement('img');
    img.src = "https://api.useadmesh.com/pixel?click_id=" + clickId + "&test=" + test;
    img.width = 1;
    img.height = 1;
    img.style.display = 'none';
    document.body.appendChild(img);
  }
</script>

3. VERIFY IMPLEMENTATION
   - Make sure the code is added to ALL versions of our thank-you page
   - Test the implementation using our test conversion tool
   - Check that URL parameters are preserved throughout the conversion flow

IMPORTANT NOTES:
---------------
• The pixel should ONLY fire when both click_id and admesh=true parameters are in the URL
• These parameters will be automatically added when users click on AdMesh affiliate links
• The code must be added to the page that appears AFTER a successful ${
                              form.goal || "signup"
                            }
• The tracking is completely invisible to users and won't affect page performance

TECHNICAL DETAILS:
-----------------
• Tracking Method: Pixel Tracking
• Offer ID: ${id}
• Conversion Type: ${form.goal || "signup"}
• API Endpoint: https://api.useadmesh.com/pixel

Please let me know when this has been implemented or if you have any questions.

Thanks!`;

                            window.open(
                              `mailto:?subject=AdMesh Conversion Tracking Implementation&body=${encodeURIComponent(
                                emailBody
                              )}`
                            );
                          }}
                        >
                          Email to Developer
                        </Button>
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="sm"
                            onClick={handleTestConversionClick}
                            disabled={isTestingConversion || !form.redirect_url}
                          >
                            {isTestingConversion
                              ? "Testing..."
                              : "Test Conversion"}
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={checkTestConversionsClick}
                            disabled={isCheckingConversions}
                          >
                            {isCheckingConversions
                              ? "Checking..."
                              : "Check Test Conversion"}
                          </Button>
                        </div>

                        {testConversionExists && (
                          <div className="mt-2 p-2 bg-green-100 rounded-md text-green-800 text-sm">
                            <p className="font-medium">
                              ✅ Test conversion detected!
                            </p>
                            <p className="text-xs mt-1">
                              Found {testConversionCount} test conversion(s).
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    <AlertDialog
                      open={testConversionDialogOpen}
                      onOpenChange={setTestConversionDialogOpen}
                    >
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Simulate a Test Conversion
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            This will create a test conversion for your offer.
                            Test conversions will be marked as &quot;test&quot;
                            in your analytics. You can run up to 3 test
                            conversions per offer.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={async () => {
                              const toastId = toast.loading(
                                "Simulating a test conversion..."
                              );
                              try {
                                // Get the Firebase auth token for API calls
                                const token =
                                  await auth.currentUser?.getIdToken();
                                if (!token) {
                                  toast.dismiss(toastId);
                                  toast.error("Authentication required");
                                  return;
                                }

                                // Make API call to simulate conversion
                                const response = await fetch(
                                  `${process.env.NEXT_PUBLIC_API_BASE_URL}/conversion/log`,
                                  {
                                    method: "POST",
                                    headers: {
                                      "Content-Type": "application/json",
                                      Authorization: `Bearer ${token}`,
                                    },
                                    body: JSON.stringify({
                                      ad_id: id,
                                      offer_id: id,
                                      event_type: form.goal || "signup",
                                      // Convert payout amount from dollars to cents for the API
                                      conversion_value:
                                        dollarsToCents(form.payout_amount) ||
                                        1000, // Default to $10.00 (1000 cents)
                                      currency: form.payout_currency || "USD",
                                      source: "test",
                                      is_test: true,
                                      // Don't send a timestamp - let the server use its own timestamp
                                      // timestamp: new Date().toISOString() // This would be an alternative if we wanted to send a timestamp
                                    }),
                                  }
                                );

                                let errorMessage =
                                  "Failed to simulate conversion";
                                if (!response.ok) {
                                  try {
                                    const errorData = await response.json();
                                    errorMessage =
                                      errorData.detail || errorMessage;
                                  } catch {
                                    // If we can't parse the error response, just use the default message
                                  }
                                  throw new Error(errorMessage);
                                }

                                // Increment the test conversion count
                                setTestConversionCount((prev) => prev + 1);

                                toast.dismiss(toastId);
                                toast.success(
                                  "Test conversion sent! Check your analytics to see the results."
                                );
                              } catch (error) {
                                toast.dismiss(toastId);
                                console.error(
                                  "Error simulating conversion:",
                                  error
                                );
                                toast.error(
                                  error instanceof Error
                                    ? error.message
                                    : "Failed to simulate conversion"
                                );
                              }
                            }}
                          >
                            Simulate Conversion
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                )}

                {form.tracking_method === "server_api" && (
                  <div className="space-y-6">
                    <div>
                      <Label
                        htmlFor="webhook_url"
                        className="text-sm font-medium"
                      >
                        Your Server Endpoint (Optional)
                      </Label>
                      <Input
                        id="webhook_url"
                        name="webhook_url"
                        value={form.webhook_url}
                        onChange={handleChange}
                        placeholder="https://yourbrand.com/api/verify-conversion"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        If provided, AdMesh will send conversion confirmations
                        to this endpoint
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        AdMesh API Endpoint
                      </Label>
                      <div className="bg-muted p-3 rounded-md font-mono text-xs overflow-x-auto">
                        {form.click_url
                          ? form.click_url.split("?")[0]
                          : "https://api.admesh.io/conversion/log"}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Your server will call this endpoint to record
                        conversions
                      </p>
                    </div>

                    <div className="border border-border rounded-lg overflow-hidden">
                      <div className="p-4 border-b">
                        <h4 className="text-sm font-medium mb-3">
                          Implementation Examples
                        </h4>
                        <Tabs
                          value={codeLanguage}
                          onValueChange={setCodeLanguage}
                          className="w-full"
                        >
                          <TabsList className="w-full grid grid-cols-4">
                            <TabsTrigger value="nodejs" className="text-xs">
                              Node.js
                            </TabsTrigger>
                            <TabsTrigger value="python" className="text-xs">
                              Python
                            </TabsTrigger>
                            <TabsTrigger value="java" className="text-xs">
                              Java
                            </TabsTrigger>
                            <TabsTrigger value="prompt" className="text-xs">
                              AI Prompt
                            </TabsTrigger>
                          </TabsList>

                          <TabsContent value="nodejs" className="mt-3">
                            <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                              <pre>{`// When a user converts on your platform
async function recordConversion(userId, conversionData) {
  // Get the stored tracking parameters for this user
  const { ad_id, click_id } = getUserTrackingParams(userId);

  // Call the AdMesh API to record the conversion
  const response = await fetch("https://api.admesh.io/conversion/log", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer YOUR_API_KEY"
    },
    body: JSON.stringify({
      offer_id: "${id || "YOUR_OFFER_ID"}",
      ad_id: ad_id,
      click_id: click_id,
      conversion_value: conversionData.value,
      conversion_type: "${form.goal || "signup"}",
      test: conversionData.test || false
    })
  });

  return response.json();
}`}</pre>
                              <Button
                                size="sm"
                                className="mt-2"
                                onClick={() => {
                                  const code = `// When a user converts on your platform
async function recordConversion(userId, conversionData) {
  // Get the stored tracking parameters for this user
  const { ad_id, click_id } = getUserTrackingParams(userId);

  // Call the AdMesh API to record the conversion
  const response = await fetch("https://api.admesh.io/conversion/log", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": "Bearer YOUR_API_KEY"
    },
    body: JSON.stringify({
      offer_id: "${id || "YOUR_OFFER_ID"}",
      ad_id: ad_id,
      click_id: click_id,
      conversion_value: conversionData.value,
      conversion_type: "${form.goal || "signup"}",
      test: conversionData.test || false
    })
  });

  return response.json();
}`;
                                  navigator.clipboard
                                    .writeText(code)
                                    .then(() => toast.success("Code copied!"));
                                }}
                              >
                                Copy Code
                              </Button>
                            </div>
                          </TabsContent>

                          <TabsContent value="python" className="mt-3">
                            <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                              <pre>{`# When a user converts on your platform
import requests
import json

def record_conversion(user_id, conversion_data):
    # Get the stored tracking parameters for this user
    tracking_params = get_user_tracking_params(user_id)
    ad_id = tracking_params.get('ad_id')
    click_id = tracking_params.get('click_id')

    # Call the AdMesh API to record the conversion
    response = requests.post(
        "https://api.admesh.io/conversion/log",
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer YOUR_API_KEY"
        },
        json={
            "offer_id": "${id || "YOUR_OFFER_ID"}",
            "ad_id": ad_id,
            "click_id": click_id,
            "conversion_value": conversion_data.get('value'),
            "conversion_type": "${form.goal || "signup"}",
            "test": conversion_data.get('test', False)
        }
    )

    return response.json()`}</pre>
                              <Button
                                size="sm"
                                className="mt-2"
                                onClick={() => {
                                  const code = `# When a user converts on your platform
import requests
import json

def record_conversion(user_id, conversion_data):
    # Get the stored tracking parameters for this user
    tracking_params = get_user_tracking_params(user_id)
    ad_id = tracking_params.get('ad_id')
    click_id = tracking_params.get('click_id')

    # Call the AdMesh API to record the conversion
    response = requests.post(
        "https://api.admesh.io/conversion/log",
        headers={
            "Content-Type": "application/json",
            "Authorization": "Bearer YOUR_API_KEY"
        },
        json={
            "offer_id": "${id || "YOUR_OFFER_ID"}",
            "ad_id": ad_id,
            "click_id": click_id,
            "conversion_value": conversion_data.get('value'),
            "conversion_type": "${form.goal || "signup"}",
            "test": conversion_data.get('test', False)
        }
    )

    return response.json()`;
                                  navigator.clipboard
                                    .writeText(code)
                                    .then(() => toast.success("Code copied!"));
                                }}
                              >
                                Copy Code
                              </Button>
                            </div>
                          </TabsContent>

                          <TabsContent value="java" className="mt-3">
                            <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                              <pre>{`// When a user converts on your platform
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.json.JSONObject;

public JSONObject recordConversion(String userId, JSONObject conversionData) throws Exception {
    // Get the stored tracking parameters for this user
    JSONObject trackingParams = getUserTrackingParams(userId);
    String adId = trackingParams.getString("ad_id");
    String clickId = trackingParams.getString("click_id");

    // Create the request body
    JSONObject requestBody = new JSONObject();
    requestBody.put("offer_id", "${id || "YOUR_OFFER_ID"}");
    requestBody.put("ad_id", adId);
    requestBody.put("click_id", clickId);
    requestBody.put("conversion_value", conversionData.getDouble("value"));
    requestBody.put("conversion_type", "${form.goal || "signup"}");
    requestBody.put("test", conversionData.optBoolean("test", false));

    // Call the AdMesh API to record the conversion
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create("https://api.admesh.io/conversion/log"))
        .header("Content-Type", "application/json")
        .header("Authorization", "Bearer YOUR_API_KEY")
        .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
        .build();

    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
    return new JSONObject(response.body());
}`}</pre>
                              <Button
                                size="sm"
                                className="mt-2"
                                onClick={() => {
                                  const code = `// When a user converts on your platform
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.json.JSONObject;

public JSONObject recordConversion(String userId, JSONObject conversionData) throws Exception {
    // Get the stored tracking parameters for this user
    JSONObject trackingParams = getUserTrackingParams(userId);
    String adId = trackingParams.getString("ad_id");
    String clickId = trackingParams.getString("click_id");

    // Create the request body
    JSONObject requestBody = new JSONObject();
    requestBody.put("offer_id", "${id || "YOUR_OFFER_ID"}");
    requestBody.put("ad_id", adId);
    requestBody.put("click_id", clickId);
    requestBody.put("conversion_value", conversionData.getDouble("value"));
    requestBody.put("conversion_type", "${form.goal || "signup"}");
    requestBody.put("test", conversionData.optBoolean("test", false));

    // Call the AdMesh API to record the conversion
    HttpClient client = HttpClient.newHttpClient();
    HttpRequest request = HttpRequest.newBuilder()
        .uri(URI.create("https://api.admesh.io/conversion/log"))
        .header("Content-Type", "application/json")
        .header("Authorization", "Bearer YOUR_API_KEY")
        .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
        .build();

    HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
    return new JSONObject(response.body());
}`;
                                  navigator.clipboard
                                    .writeText(code)
                                    .then(() => toast.success("Code copied!"));
                                }}
                              >
                                Copy Code
                              </Button>
                            </div>
                          </TabsContent>

                          <TabsContent value="prompt" className="mt-3">
                            <div className="bg-white font-mono text-xs overflow-x-auto p-4 border rounded-md">
                              <pre>{`I need to implement server-side conversion tracking for AdMesh in my application. Here are the details:

- Offer ID: ${id || "YOUR_OFFER_ID"}
- Tracking Method: Server-side API
- API Endpoint: https://api.admesh.io/conversion/log
- Conversion Type: ${form.goal || "signup"}
- Programming Language: [SPECIFY YOUR LANGUAGE: Node.js, Python, Java, PHP, Ruby, etc.]

When a user completes a conversion on our platform, we need to:
1. Retrieve the tracking parameters (ad_id and click_id) that were stored when the user initially clicked the affiliate link
2. Send these parameters along with the conversion data to the AdMesh API

Please write the code to implement this server-side conversion tracking. Include:
- A function to record conversions
- The API call with proper headers and payload
- Error handling
- Any necessary imports or dependencies`}</pre>
                              <Button
                                size="sm"
                                className="mt-2"
                                onClick={() => {
                                  const code = `I need to implement server-side conversion tracking for AdMesh in my application. Here are the details:

- Offer ID: ${id || "YOUR_OFFER_ID"}
- Tracking Method: Server-side API
- API Endpoint: https://api.admesh.io/conversion/log
- Conversion Type: ${form.goal || "signup"}
- Programming Language: [SPECIFY YOUR LANGUAGE: Node.js, Python, Java, PHP, Ruby, etc.]

When a user completes a conversion on our platform, we need to:
1. Retrieve the tracking parameters (ad_id and click_id) that were stored when the user initially clicked the affiliate link
2. Send these parameters along with the conversion data to the AdMesh API

Please write the code to implement this server-side conversion tracking. Include:
- A function to record conversions
- The API call with proper headers and payload
- Error handling
- Any necessary imports or dependencies`;
                                  navigator.clipboard
                                    .writeText(code)
                                    .then(() =>
                                      toast.success("Prompt copied!")
                                    );
                                }}
                              >
                                Copy Prompt
                              </Button>
                            </div>
                          </TabsContent>
                        </Tabs>
                      </div>
                    </div>

                    <div className="border border-border rounded-lg overflow-hidden mt-4">
                      <div className="p-4 border-b">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">How It Works</h4>
                        </div>
                        <div className="mt-3">
                          <ol className="list-decimal pl-5 text-sm space-y-2 text-muted-foreground">
                            <li>
                              User clicks an affiliate link and is redirected to
                              your site with tracking parameters
                            </li>
                            <li>
                              You store the tracking parameters (ad_id,
                              click_id) in your database or session
                            </li>
                            <li>
                              When a conversion happens, your server calls our
                              API with the conversion data
                            </li>
                            <li>
                              We verify the conversion, credit the affiliate,
                              and send a confirmation response
                            </li>
                          </ol>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md flex items-start mt-4">
                <Info className="w-5 h-5 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800 mb-1">
                    Need Help?
                  </h4>
                  <p className="text-sm text-blue-700 mb-2">
                    Contact us for implementation assistance:
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                      onClick={() =>
                        window.open(
                          "https://calendly.com/gounimanikumar/30min",
                          "_blank"
                        )
                      }
                    >
                      Schedule Implementation Call
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                      onClick={() =>
                        window.open(
                          "mailto:<EMAIL>?subject=AdMesh%20Implementation%20Assistance&body=Hi%20Mani%2C%0A%0AI%20need%20help%20with%20implementing%20AdMesh%20tracking%20for%20my%20offer.%20Could%20you%20please%20assist%20me%20with%20the%20following%3A%0A%0A-%20%5BDescribe%20your%20implementation%20question%20or%20issue%5D%0A%0AThanks%2C%0A%5BYour%20Name%5D"
                        )
                      }
                    >
                      Email: <EMAIL>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                      onClick={() =>
                        window.open(
                          "https://www.linkedin.com/in/manikumargouni/",
                          "_blank"
                        )
                      }
                    >
                      LinkedIn Profile
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>

            {/* Additional Information Section */}
            {originalOffer && (
              <CardContent className="pt-0 pb-6 border-t bg-gray-50 dark:bg-gray-900/50">
                <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Additional Information
                </h3>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  <div>
                    <Label className="text-xs text-muted-foreground">Offer ID</Label>
                    <div className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                      {originalOffer.offer_id || id}
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Product ID</Label>
                    <div className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                      {originalOffer.product_id || "N/A"}
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Brand ID</Label>
                    <div className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                      {originalOffer.brand_id || "N/A"}
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Trust Score</Label>
                    <div className="text-sm font-semibold text-green-600">
                      {originalOffer.trust_score || 100}/100
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Created</Label>
                    <div className="text-sm">
                      {originalOffer.created_at ?
                        typeof originalOffer.created_at === 'string'
                          ? new Date(originalOffer.created_at).toLocaleDateString()
                          : new Date(originalOffer.created_at.seconds * 1000).toLocaleDateString()
                        : "N/A"
                      }
                    </div>
                  </div>

                  <div>
                    <Label className="text-xs text-muted-foreground">Budget Spent</Label>
                    <div className="text-sm font-semibold">
                      ${centsToDollars(originalOffer.offer_total_budget_spent || 0).toFixed(2)} / ${centsToDollars(originalOffer.offer_total_budget_allocated || 0).toFixed(2)}
                    </div>
                  </div>
                </div>
              </CardContent>
            )}

            <CardFooter className="flex justify-between border-t p-6 bg-gray-50">
              <Button
                variant="outline"
                onClick={handleCancel}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpdate}
                disabled={saving || !hasChanges}
                className="px-8"
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </CardFooter>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Unsaved Changes Dialog */}
      <AlertDialog
        open={unsavedChangesDialogOpen}
        onOpenChange={setUnsavedChangesDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Discard unsaved changes?</AlertDialogTitle>
            <AlertDialogDescription>
              You have unsaved changes. If you leave this page, all your changes
              will be lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Continue Editing</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDiscardChanges}
              className="bg-red-600 hover:bg-red-700"
            >
              Discard Changes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
