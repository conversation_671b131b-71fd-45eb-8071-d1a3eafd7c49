"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { centsToDollars, formatCurrency } from "@/lib/utils";
import { toast } from "sonner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/hooks/use-auth";
import OfferActivationDialog from "@/components/OfferActivationDialog";
import {
  <PERSON><PERSON><PERSON>,
  ArrowUpRight,
  MousePointerClick,
  Clock,
  XCircle,
  Wallet,
  RotateCcw,
  DollarSign,
  // Filter, // Uncomment when needed
} from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";

// Utility function to truncate text
const truncateText = (text: string, maxLength: number = 15): string => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};

export default function OffersPage() {
  const { user } = useAuth();
  const router = useRouter();
  interface Offer {
    id: string;
    offer_display_id?: string; // Display ID for URLs
    status: string; // Backward compatibility
    offer_status: "active" | "inactive" | "cancelled"; // Primary status field
    active: boolean; // Backward compatibility
    clicks: number;
    conversions: number;
    conversions_production?: number; // Added for production conversions
    budget: number;
    offer_total_budget_allocated: number;
    total_spent_offer: number;
    total_spent_production?: number; // Added for production spending
    total_spent_test?: number; // Added for test spending
    view_count?: number; // Added for product views
    ctr: string | number;
    reward: string;
    updatedAt: string;
    url: string;
    keywords: string[];
    categories: string[];
    // Marketing content fields
    offer_title: string;
    offer_description: string;
    feature_sections?: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  }

  interface OfferApiResponse {
    id: string;
    offer_id?: string; // Sometimes returned from backend
    offer_display_id?: string; // Display ID for URLs
    title: string;
    offer_title?: string; // New marketing title field
    offer_description?: string; // New marketing description field
    feature_sections?: Array<{
      title: string;
      description: string;
      icon: string;
    }>; // Feature sections array
    active: boolean; // Backward compatibility
    offer_status: "active" | "inactive" | "cancelled"; // Primary status field
    status: string; // Backward compatibility
    brand_id: string;
    product_id: string;

    // Click tracking
    clicks?: number; // Added for backward compatibility
    click_count: number | {
      test: number;
      total: number;
      production?: number;
    };
    clicks_production?: number; // Sometimes returned directly
    clicks_test?: number; // Sometimes returned directly

    // Conversion tracking
    conversions?: number; // Added for backward compatibility
    conversion_count: number | {
      production: number;
      test: number;
      total: number;
    };
    conversions_test?: number;
    conversions_production?: number;
    conversions_total?: number;

    // Financial data
    budget: number;
    offer_total_budget_allocated?: number;
    offer_total_budget_spent?: number;
    offer_total_promo_spent?: number;
    offer_total_promo_available?: number;
    total_spent_offer: number;
    total_spent?: number | {
      production: number;
      test: number;
      all: number;
    };

    // Offer details
    view_count?: number; // Added for product views
    reward_note: string;
    payout: {
      amount: number;
      currency: string;
      model?: string; // CPA, CPL, CPI, RevShare
    } | null;
    goal?: string; // signup, purchase, lead, app_install, click

    // Tracking and integration
    tracking?: {
      method?: string; // redirect_pixel, server_api, manual
      webhook_url?: string;
      notes?: string;
      redirect_url?: string;
      target_urls?: string[];
    };

    // Timestamps and metadata
    created_at?: string | null;
    last_converted_at: string | null;
    updated_at?: string | null;

    // Content
    description: string;
    url: string;
    keywords: string[];
    categories: string[];

    // Additional fields
    trust_score?: number;
    suggestion_reason?: string;
    valid_until?: string | null;
    meta?: Record<string, unknown>;
    ctr?: number;
    spent?: number; // Sometimes returned directly
  }

  interface ApiTotals {
    clicks: number; // Total clicks (production + test)
    clicks_production?: number; // Production clicks only
    clicks_test?: number; // Test clicks only
    conversions: number; // Production conversions
    conversions_test?: number; // Test conversions
    conversions_all?: number; // Total conversions (production + test)
    budget: number;
    total_spent_offer: number;
    total_spent?: {
      production: number;
      test: number;
      all: number;
    };
    view_count?: number; // Combined product views + offer views
    product_views?: number; // Product views only
    offer_views?: {
      total: number;
      production: number;
      test: number;
    }; // Offer views
    ctr: number;
  }

  const [offers, setOffers] = useState<Offer[]>([]);
  const [totals, setTotals] = useState<ApiTotals>({
    clicks: 0,
    clicks_production: 0,
    clicks_test: 0,
    conversions: 0,
    conversions_test: 0,
    conversions_all: 0,
    budget: 0,
    total_spent_offer: 0,
    total_spent: {
      production: 0,
      test: 0,
      all: 0
    },
    view_count: 0,
    product_views: 0,
    offer_views: {
      total: 0,
      production: 0,
      test: 0
    },
    ctr: 0,
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [showActivationDialog, setShowActivationDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [cancellingOffer, setCancellingOffer] = useState(false);
  const [showReinstateDialog, setShowReinstateDialog] = useState(false);
  const [reinstatingOffer, setReinstatingOffer] = useState(false);
  const [budgetAmount, setBudgetAmount] = useState<string>("");
  const [walletBalance, setWalletBalance] = useState<number>(0);
  const [hasActiveOffer, setHasActiveOffer] = useState(false);
  const [detailedOffer, setDetailedOffer] = useState<OfferApiResponse | null>(null);

  useEffect(() => {
    if (!user) return;

    const fetchOffers = async () => {
      setLoading(true);
      try {
        const token = await user.getIdToken();

        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!res.ok) {
          throw new Error("Failed to fetch offers");
        }

        const data = await res.json();
        console.log(data);

        // Set totals from API response if available
        if (data.totals) {
          setTotals(data.totals);
        }

        // Log the data to see what we're getting
        console.log('API response totals:', data.totals);
        console.log('API response offers:', data.offers);

        // Check if there are any active offers
        const activeOffers = data.offers.filter((offer: OfferApiResponse) => offer.offer_status === "active");
        setHasActiveOffer(activeOffers.length > 0);

        const results = data.offers.map((offer: OfferApiResponse) => {
          // Extract production click count from either the new object structure or fallback to old format
          // Explicitly use only production clicks, not test clicks or total clicks
          const clickCount = typeof offer.click_count === 'object'
            ? (offer.click_count.production || 0)
            : (offer.clicks || offer.click_count || 0);

          // Extract production conversion count from either the new object structure or fallback to old format
          // Explicitly use only production conversions, not test conversions
          const conversionCount = typeof offer.conversion_count === 'object'
            ? offer.conversion_count.production || 0
            : (offer.conversions_production || 0);

          return {
            id: offer.id,
            offer_display_id: offer.offer_display_id,
            title: offer.title || "Untitled",
            status: offer.status || offer.offer_status || "inactive", // Use API status
            offer_status: offer.offer_status || offer.status || "inactive", // Primary status field
            active: offer.active || (offer.offer_status === "active"), // Backward compatibility
            clicks: clickCount,
            // Only use production conversions
            conversions: conversionCount,
            conversions_production: conversionCount,
            // Budget is stored in cents in the database, convert to dollars for display
            budget: offer.budget ? centsToDollars(offer.budget) : 0,
            // Get offer_total_budget_allocated
            offer_total_budget_allocated: offer.offer_total_budget_allocated ? centsToDollars(offer.offer_total_budget_allocated) : 0,
            // Only use production spending, convert from cents to dollars
            total_spent_offer: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent.production || 0) :
              centsToDollars(offer.total_spent_offer || 0),
            // Add production and test spending if available, convert from cents to dollars
            total_spent_production: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent?.production || 0) : undefined,
            total_spent_test: typeof offer.total_spent === 'object' ?
              centsToDollars(offer.total_spent?.test || 0) : undefined,
            // Include view_count from the API response
            view_count: offer.view_count || 0,
            // Calculate CTR using only production conversions and production clicks
            // If there are no production clicks, CTR is 0
            ctr: clickCount > 0 && conversionCount >= 0
              ? ((conversionCount / clickCount) * 100).toFixed(1)
              : 0,
            reward:
              offer.reward_note ||
              (offer.payout?.amount
                ? formatCurrency(centsToDollars(offer.payout.amount), offer.payout.currency ?? "USD")
              : "—"),
            updatedAt: offer.last_converted_at
              ? format(new Date(offer.last_converted_at), "MMM dd, yyyy")
              : "—",
            url: offer.url || "",
            keywords: offer.keywords || [],
            categories: offer.categories || [],
            // Marketing content fields
            offer_title: offer.offer_title || "Untitled Offer",
            offer_description: offer.offer_description || "",
            feature_sections: offer.feature_sections || [],
          };
        });

        setOffers(results);
      } catch (err) {
        console.error("Error fetching offers:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, [user]);

  const fetchWalletBalance = async () => {
    if (!user) return;

    try {
      const token = await user.getIdToken();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/brands/${user.uid}/wallet`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setWalletBalance(data.wallet?.total_available_balance || 0);
      }
    } catch (error) {
      console.error("Error fetching wallet balance:", error);
    }
  };

  const handleReinstateOffer = async () => {
    if (!selectedOffer || !user || !budgetAmount) return;

    const budget = parseFloat(budgetAmount);
    if (isNaN(budget) || budget <= 0) {
      toast.error("Please enter a valid budget amount");
      return;
    }

    if (budget > walletBalance) {
      toast.error("Insufficient wallet balance");
      return;
    }

    try {
      setReinstatingOffer(true);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/${selectedOffer.offer_display_id || selectedOffer.id}/reinstate`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            budget_amount: budget
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to reinstate offer");
      }

      const result = await response.json();

      toast.success(
        `Offer reinstated successfully! $${result.budget_allocated.toFixed(2)} allocated from your wallet.`
      );

      // Refresh offers list
      window.location.reload();

    } catch (error) {
      console.error("Error reinstating offer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to reinstate offer");
    } finally {
      setReinstatingOffer(false);
      setShowReinstateDialog(false);
      setSelectedOffer(null);
      setBudgetAmount("");
    }
  };

  const handleCancelOffer = async () => {
    if (!selectedOffer || !user) return;

    try {
      setCancellingOffer(true);
      const token = await user.getIdToken();

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/${selectedOffer.offer_display_id || selectedOffer.id}/cancel`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to cancel offer");
      }

      const result = await response.json();

      toast.success(
        `Offer cancelled successfully! ${result.remaining_budget_refunded > 0
          ? `$${result.remaining_budget_refunded.toFixed(2)} refunded to your wallet.`
          : 'No remaining budget to refund.'
        }`
      );

      // Refresh offers list
      window.location.reload();

    } catch (error) {
      console.error("Error cancelling offer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to cancel offer");
    } finally {
      setCancellingOffer(false);
      setShowCancelDialog(false);
      setSelectedOffer(null);
    }
  };

  const activeOffers = offers.filter((offer) => offer.offer_status === "active");
  const inactiveOffers = offers.filter((offer) => offer.offer_status === "inactive");
  const cancelledOffers = offers.filter((offer) => offer.offer_status === "cancelled");

  const displayOffers =
    activeTab === "active"
      ? activeOffers
      : activeTab === "inactive"
      ? inactiveOffers
      : activeTab === "cancelled"
      ? cancelledOffers
      : offers;

  // Use totals from API or calculate if not available
  // Make sure we only count production clicks, not test clicks
  const totalClicks =
    totals.clicks_production || offers.reduce((sum, offer) => sum + offer.clicks, 0);

  // Only use production conversions
  const totalConversions =
    totals.conversions ||
    offers.reduce((sum, offer) => sum + (offer.conversions_production || 0), 0);

  // We no longer need budget and spent calculations since we removed those cards

  // No longer need budget usage percentage since we removed the budget cards

  return (
    <div className="min-h-screen bg-gray-50/30 dark:bg-gray-900/30">
      {/* Header Section */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white tracking-tight">
                  Promotional Offers
                </h1>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">
                  Manage and optimize your affiliate campaigns to drive conversions
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => {
                    if (hasActiveOffer) {
                      toast.error("You can only have one active offer at a time. Please deactivate your existing offer before creating a new one.");
                    } else {
                      router.push("/dashboard/brand/offers/new");
                    }
                  }}
                  className="bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100 text-white font-medium px-6 py-2 rounded-lg transition-all duration-200"
                >
                  + Create Offer
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto space-y-8">

          {/* Performance Overview */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Performance Overview</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6">
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-900 dark:hover:border-gray-500 hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700">
                          <BarChart className="text-gray-700 dark:text-gray-300 h-5 w-5" />
                        </div>
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                          Total Budget
                        </p>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        ${offers.reduce((sum, offer) => sum + offer.offer_total_budget_allocated, 0).toFixed(2)}
                      </h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Allocated across all offers</span>
                        <div className="h-2 w-2 bg-gray-900 dark:bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-900 dark:hover:border-gray-500 hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700">
                          <ArrowUpRight className="text-gray-700 dark:text-gray-300 h-5 w-5" />
                        </div>
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                          Total Spent
                        </p>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                        ${offers.reduce((sum, offer) => sum + offer.total_spent_offer, 0).toFixed(2)}
                      </h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Production conversions only</span>
                        <div className="h-2 w-2 bg-gray-900 dark:bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-900 dark:hover:border-gray-500 hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700">
                          <BarChart className="text-gray-700 dark:text-gray-300 h-5 w-5" />
                        </div>
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                          Total Views
                        </p>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{totals.view_count || 0}</h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {totals.product_views || 0} product + {totals.offer_views?.production || 0} offer
                        </span>
                        <div className="h-2 w-2 bg-gray-900 dark:bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-900 dark:hover:border-gray-500 hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700">
                          <MousePointerClick className="text-gray-700 dark:text-gray-300 h-5 w-5" />
                        </div>
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                          Total Clicks
                        </p>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{totalClicks}</h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Production clicks only</span>
                        <div className="h-2 w-2 bg-gray-900 dark:bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:border-gray-900 dark:hover:border-gray-500 hover:shadow-lg transition-all duration-200">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="p-3 rounded-xl bg-gray-100 dark:bg-gray-700">
                          <ArrowUpRight className="text-gray-700 dark:text-gray-300 h-5 w-5" />
                        </div>
                        <p className="text-sm font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">
                          Conversions
                        </p>
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{totalConversions}</h3>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Production conversions only</span>
                        <div className="h-2 w-2 bg-gray-900 dark:bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
          {/* Offers Table Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Offers Overview</h2>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                  <TabsTrigger value="all" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm">
                    All Offers
                  </TabsTrigger>
                  <TabsTrigger value="active" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm">
                    Active ({activeOffers.length})
                  </TabsTrigger>
                  <TabsTrigger value="inactive" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm">
                    Inactive ({inactiveOffers.length})
                  </TabsTrigger>
                  <TabsTrigger value="cancelled" className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:shadow-sm">
                    Cancelled ({cancelledOffers.length})
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm">
              <CardContent className="p-0">
                <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Spent</TableHead>
                <TableHead>Clicks</TableHead>
                <TableHead>Conversions</TableHead>
                <TableHead>CTR</TableHead>
                <TableHead>Last Converted</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell
                    colSpan={10}
                    className="text-center py-6 text-sm text-muted-foreground"
                  >
                    Loading offers...
                  </TableCell>
                </TableRow>
              ) : displayOffers.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={10}
                    className="text-center py-10 text-sm text-muted-foreground"
                  >
                    {activeTab === "all"
                      ? "No offers yet. Click Create Offer to get started."
                      : activeTab === "active"
                      ? "No active offers. Activate an offer or create a new one."
                      : "No inactive offers."}
                  </TableCell>
                </TableRow>
              ) : (
                displayOffers.map((offer) => (
                  <TableRow
                    key={offer.id}
                    className="cursor-pointer hover:bg-muted/50 dark:hover:bg-gray-700/50 transition"
                    onClick={() =>
                      router.push(`/dashboard/brand/offers/${offer.offer_display_id || offer.id}/analytics`)
                    }
                  >
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium" title={offer.offer_title}>
                          {truncateText(offer.offer_title, 15)}
                        </span>
                        {offer.offer_description && (
                          <span className="text-xs text-muted-foreground mt-1 line-clamp-1" title={offer.offer_description}>
                            {truncateText(offer.offer_description, 12)}
                          </span>
                        )}
                        {offer.keywords.length > 0 && (
                          <div className="flex mt-1 gap-1">
                            {offer.keywords.slice(0, 2).map((keyword) => (
                              <Badge
                                key={keyword}
                                variant="secondary"
                                className="text-xs px-1"
                              >
                                {keyword}
                              </Badge>
                            ))}
                            {offer.keywords.length > 2 && (
                              <Badge
                                variant="secondary"
                                className="text-xs px-1"
                              >
                                +{offer.keywords.length - 2}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          offer.offer_status === "active"
                            ? "bg-green-100 text-green-700 border-green-200"
                            : offer.offer_status === "cancelled"
                            ? "bg-red-100 text-red-700 border-red-200"
                            : "bg-gray-100 text-gray-600 border-gray-200"
                        }
                      >
                        {offer.status}
                      </Badge>
                    </TableCell>
                  
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">${offer.offer_total_budget_allocated}</span>
                        <span className="text-xs text-muted-foreground">
                          ${(offer.offer_total_budget_allocated - offer.total_spent_offer).toFixed(2)} remaining
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">${offer.total_spent_offer}</span>
                        <div className="flex gap-1 text-xs">
                          {offer.total_spent_production !== undefined && (
                            <span className="text-green-600">
                              Prod: ${offer.total_spent_production}
                            </span>
                          )}
                          {offer.total_spent_test !== undefined && offer.total_spent_test > 0 && (
                            <span className="text-blue-600">
                              Test: ${offer.total_spent_test}
                            </span>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{offer.clicks}</TableCell>
                    <TableCell>{offer.conversions}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <span
                          className={
                            Number(offer.ctr) > 0
                              ? "text-green-600"
                              : "text-gray-500"
                          }
                        >
                          {offer.ctr}%
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {offer.updatedAt}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex gap-2 justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              `/dashboard/brand/offers/${offer.id}/analytics`
                            );
                          }}
                        >
                          Analytics
                        </Button>
                        {/* <Button
                          variant="outline"
                          size="sm"
                          onClick={async (e) => {
                            e.stopPropagation();
                            setLoadingDetails(true);
                            const offerDetails = await fetchOfferById(offer.id);
                            setDetailedOffer(offerDetails);
                            setLoadingDetails(false);
                            if (offerDetails) {
                              toast.success(`Fetched details for ${offerDetails.title}`);
                              console.log("Offer details:", offerDetails);
                            } else {
                              toast.error(`Failed to fetch details for ${offer.title}`);
                            }
                          }}
                        >
                          Details
                        </Button> */}
                        <Button
                          variant={
                            offer.offer_status === "active"
                              ? "destructive"
                              : "default"
                          }
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedOffer(offer);

                            setShowActivationDialog(true);
                          }}
                        >
                          {offer.offer_status === "active" ? "Pause" : "Activate"}
                        </Button>

                        {/* Cancel Button - only show for active offers */}
                        {offer.offer_status === "active" && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-200 hover:bg-red-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedOffer(offer);
                              setShowCancelDialog(true);
                            }}
                          >
                            <XCircle className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                        )}

                        {/* Reinstate Button - only show for cancelled offers */}
                        {offer.offer_status === "cancelled" && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-200 hover:bg-green-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedOffer(offer);
                              fetchWalletBalance();
                              setShowReinstateDialog(true);
                            }}
                          >
                            <RotateCcw className="h-4 w-4 mr-1" />
                            Reinstate
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
                </TableBody>
              </Table>
              </CardContent>
            </Card>
          </div>

      {/* Offer Details Dialog */}
      {detailedOffer && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">{detailedOffer.title} Details</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDetailedOffer(null)}
              >
                Close
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">Basic Information</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>ID: <span className="font-mono">{detailedOffer.id}</span></div>
                  <div>Status: <Badge>{detailedOffer.active ? "Active" : "Inactive"}</Badge></div>
                  <div>Product ID: <span className="font-mono">{detailedOffer.product_id}</span></div>
                  <div>Brand ID: <span className="font-mono">{detailedOffer.brand_id}</span></div>
                  <div>Goal: {detailedOffer.goal || "Not specified"}</div>
                  <div>Trust Score: {detailedOffer.trust_score || "50"}</div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Metrics</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Production Clicks: {typeof detailedOffer.click_count === 'object' ?
                    detailedOffer.click_count.production || 0 : detailedOffer.click_count || 0}</div>
                  <div>Test Clicks: {typeof detailedOffer.click_count === 'object' ?
                    detailedOffer.click_count.test || 0 : 0}</div>
                  <div>Production Conversions: {typeof detailedOffer.conversion_count === 'object' ?
                    detailedOffer.conversion_count.production || 0 : detailedOffer.conversion_count || 0}</div>
                  <div>Test Conversions: {typeof detailedOffer.conversion_count === 'object' ?
                    detailedOffer.conversion_count.test || 0 : 0}</div>
                  <div>Budget: ${centsToDollars(detailedOffer.budget || 0)}</div>
                  <div>Total Spent: ${typeof detailedOffer.total_spent === 'object' ?
                    centsToDollars(detailedOffer.total_spent.production || 0) :
                    centsToDollars(detailedOffer.total_spent_offer || 0)}</div>
                </div>
              </div>

              {detailedOffer.tracking && (
                <div>
                  <h3 className="font-semibold mb-2">Tracking</h3>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>Method: {detailedOffer.tracking.method || "Not specified"}</div>
                    <div>Redirect URL: {detailedOffer.tracking.redirect_url || "Not specified"}</div>
                    <div>Webhook URL: {detailedOffer.tracking.webhook_url || "Not specified"}</div>
                    <div>Target URLs: {detailedOffer.tracking.target_urls?.length || 0} URLs</div>
                  </div>
                </div>
              )}

              <div>
                <h3 className="font-semibold mb-2">Payout</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Amount: {detailedOffer.payout ?
                    formatCurrency(centsToDollars(detailedOffer.payout.amount),
                    detailedOffer.payout.currency || "USD") : "Not specified"}</div>
                  <div>Model: {detailedOffer.payout?.model || "CPA"}</div>
                  <div>Reward Note: {detailedOffer.reward_note || "Not specified"}</div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Content</h3>
                <div className="text-sm">
                  <div className="mb-2">Description: {detailedOffer.description}</div>
                  <div className="mb-2">URL: <a href={detailedOffer.url} target="_blank" rel="noopener noreferrer"
                    className="text-blue-600 hover:underline">{detailedOffer.url}</a></div>
                  <div className="mb-2">
                    Categories: {detailedOffer.categories.map(cat => (
                      <Badge key={cat} variant="outline" className="mr-1">{cat}</Badge>
                    ))}
                  </div>
                  <div>
                    Keywords: {detailedOffer.keywords.map(keyword => (
                      <Badge key={keyword} variant="secondary" className="mr-1">{keyword}</Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold mb-2">Timestamps</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Created: {detailedOffer.created_at ?
                    format(new Date(detailedOffer.created_at), "MMM dd, yyyy HH:mm") : "Not available"}</div>
                  <div>Last Converted: {detailedOffer.last_converted_at ?
                    format(new Date(detailedOffer.last_converted_at), "MMM dd, yyyy HH:mm") : "Never"}</div>
                  <div>Updated: {detailedOffer.updated_at ?
                    format(new Date(detailedOffer.updated_at), "MMM dd, yyyy HH:mm") : "Not available"}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Offer Activation Dialog */}
      {selectedOffer && (
        <OfferActivationDialog
          isOpen={showActivationDialog}
          onClose={() => {
            setShowActivationDialog(false);
            // Refresh the offers list after dialog closes
            if (user) {
              const fetchOffers = async () => {
                try {
                  const token = await user.getIdToken();
                  const res = await fetch(
                    `${process.env.NEXT_PUBLIC_API_BASE_URL}/offers/brand/all`,
                    {
                      headers: {
                        Authorization: `Bearer ${token}`,
                      },
                    }
                  );
                  if (res.ok) {
                    const data = await res.json();
                    if (data.totals) {
                      setTotals(data.totals);
                    }
                    // Log the data to see what we're getting
                    console.log('API response totals:', data.totals);
                    console.log('API response offers:', data.offers);

                    // Check if there are any active offers
                    const activeOffers = data.offers.filter((offer: OfferApiResponse) => offer.offer_status === "active");
                    setHasActiveOffer(activeOffers.length > 0);

                    const results = data.offers.map(
                      (offer: OfferApiResponse) => {
                        // Extract production click count from either the new object structure or fallback to old format
                        // Explicitly use only production clicks, not test clicks or total clicks
                        const clickCount = typeof offer.click_count === 'object'
                          ? (offer.click_count.production || 0)
                          : (offer.clicks || offer.click_count || 0);

                        // Extract production conversion count from either the new object structure or fallback to old format
                        // Explicitly use only production conversions, not test conversions
                        const conversionCount = typeof offer.conversion_count === 'object'
                          ? offer.conversion_count.production || 0
                          : (offer.conversions_production || 0);

                        return {
                          id: offer.id,
                          title: offer.title || "Untitled",
                          status: offer.status || offer.offer_status || "inactive", // Use API status
                          offer_status: offer.offer_status || offer.status || "inactive", // Primary status field
                          active: offer.active || (offer.offer_status === "active"), // Backward compatibility
                          clicks: clickCount,
                          // Only use production conversions
                          conversions: conversionCount,
                          conversions_production: conversionCount,
                          // Budget is stored in cents in the database, convert to dollars for display
                          budget: offer.budget ? centsToDollars(offer.budget) : 0,
                          // Get offer_total_budget_allocated
                          offer_total_budget_allocated: offer.offer_total_budget_allocated ? centsToDollars(offer.offer_total_budget_allocated) : 0,
                          // Only use production spending, convert from cents to dollars
                          total_spent_offer: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent.production || 0) :
                            centsToDollars(offer.total_spent_offer || 0),
                          // Add production and test spending if available, convert from cents to dollars
                          total_spent_production: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent?.production || 0) : undefined,
                          total_spent_test: typeof offer.total_spent === 'object' ?
                            centsToDollars(offer.total_spent?.test || 0) : undefined,
                          // Include view_count from the API response
                          view_count: offer.view_count || 0,
                          // Calculate CTR using only production conversions and production clicks
                          // If there are no production clicks, CTR is 0
                          ctr: clickCount > 0 && conversionCount >= 0
                            ? ((conversionCount / clickCount) * 100).toFixed(1)
                            : 0,
                          reward:
                            offer.reward_note ||
                            (offer.payout?.amount
                              ? formatCurrency(centsToDollars(offer.payout.amount), offer.payout.currency ?? "USD")
                              : "—"),
                          updatedAt: offer.last_converted_at
                            ? format(new Date(offer.last_converted_at), "MMM dd, yyyy")
                            : "—",
                          description: offer.description || "",
                          url: offer.url || "",
                          keywords: offer.keywords || [],
                          categories: offer.categories || [],
                        };
                      }
                    );
                    setOffers(results);
                  }
                } catch (err) {
                  console.error("Error refreshing offers:", err);
                }
              };
              fetchOffers();
            }
          }}
          offerId={selectedOffer.id}
          offerTitle={selectedOffer.offer_title}
          offerBudget={selectedOffer.offer_total_budget_allocated}
          isActive={selectedOffer.offer_status === "active"}
          offerMarketingTitle={selectedOffer.offer_title}
        />
      )}

      {/* Cancel Offer Dialog */}
      {showCancelDialog && selectedOffer && (
        <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <XCircle className="h-5 w-5 text-red-500" />
                Cancel Offer
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to cancel "{selectedOffer.offer_title || 'this offer'}"?
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <Wallet className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-800">Budget Refund</h4>
                    <p className="text-sm text-amber-700 mt-1">
                      Any remaining budget will be refunded to your wallet:
                    </p>
                    <div className="mt-2 text-sm">
                      <div className="flex justify-between">
                        <span>Total Budget:</span>
                        <span className="font-medium">${selectedOffer.offer_total_budget_allocated?.toFixed(2) || '0.00'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Amount Spent:</span>
                        <span className="font-medium">${selectedOffer.total_spent_offer?.toFixed(2) || '0.00'}</span>
                      </div>
                      <div className="flex justify-between border-t border-amber-300 pt-1 mt-1">
                        <span className="font-medium">Refund Amount:</span>
                        <span className="font-medium text-green-700">
                          ${((selectedOffer.offer_total_budget_allocated || 0) - (selectedOffer.total_spent_offer || 0)).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-700">
                  <strong>Warning:</strong> This action cannot be undone. The offer will be permanently cancelled and removed from all agent recommendations.
                </p>
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setShowCancelDialog(false)}
                disabled={cancellingOffer}
              >
                Keep Offer
              </Button>
              <Button
                variant="destructive"
                onClick={handleCancelOffer}
                disabled={cancellingOffer}
                className="bg-red-600 hover:bg-red-700"
              >
                {cancellingOffer ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Cancelling...
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel Offer
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Reinstate Offer Dialog */}
      {showReinstateDialog && selectedOffer && (
        <Dialog open={showReinstateDialog} onOpenChange={setShowReinstateDialog}>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <RotateCcw className="h-5 w-5 text-green-500" />
                Reinstate Offer
              </DialogTitle>
              <DialogDescription>
                Reinstate "{selectedOffer.offer_title || 'this offer'}" with a new budget allocation.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Wallet Balance Display */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <Wallet className="h-5 w-5 text-blue-600" />
                  <div>
                    <h4 className="font-medium text-blue-800">Available Wallet Balance</h4>
                    <p className="text-lg font-semibold text-blue-900">${walletBalance.toFixed(2)}</p>
                  </div>
                </div>
              </div>

              {/* Budget Input */}
              <div className="space-y-2">
                <label htmlFor="budget-amount" className="text-sm font-medium text-gray-700">
                  New Budget Amount
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    id="budget-amount"
                    type="number"
                    min="1"
                    max={walletBalance}
                    step="0.01"
                    value={budgetAmount}
                    onChange={(e) => setBudgetAmount(e.target.value)}
                    placeholder="Enter budget amount"
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Minimum: $1.00 • Maximum: ${walletBalance.toFixed(2)}
                </p>
              </div>

              {/* Budget Breakdown */}
              {budgetAmount && !isNaN(parseFloat(budgetAmount)) && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h4 className="font-medium text-green-800 mb-2">Budget Allocation Summary</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Budget to Allocate:</span>
                      <span className="font-medium">${parseFloat(budgetAmount).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Remaining Wallet Balance:</span>
                      <span className="font-medium">
                        ${(walletBalance - parseFloat(budgetAmount)).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Warning */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <p className="text-sm text-amber-700">
                  <strong>Note:</strong> Reinstating this offer will reset all performance metrics (clicks, conversions, spent amounts) to zero for a fresh start.
                </p>
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowReinstateDialog(false);
                  setBudgetAmount("");
                }}
                disabled={reinstatingOffer}
              >
                Cancel
              </Button>
              <Button
                onClick={handleReinstateOffer}
                disabled={
                  reinstatingOffer ||
                  !budgetAmount ||
                  isNaN(parseFloat(budgetAmount)) ||
                  parseFloat(budgetAmount) <= 0 ||
                  parseFloat(budgetAmount) > walletBalance
                }
                className="bg-green-600 hover:bg-green-700"
              >
                {reinstatingOffer ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Reinstating...
                  </>
                ) : (
                  <>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reinstate Offer
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

        </div>
      </div>

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}
