#!/usr/bin/env python3
"""
Debug script to check if <PERSON>lue<PERSON> has active offers.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


def find_clueso_offers():
    """Find all offers for Clueso product."""
    logger.info("🔍 Searching for Clueso offers...")
    
    # First find Clueso product ID
    products_ref = db.collection("products")
    products = products_ref.stream()
    
    clueso_product_id = None
    for product_doc in products:
        product_data = product_doc.to_dict()
        title = product_data.get("title", "").lower()
        
        if "clueso" in title:
            clueso_product_id = product_doc.id
            logger.info(f"✅ Found Clueso product: {clueso_product_id}")
            logger.info(f"   Title: {product_data.get('title')}")
            logger.info(f"   Description: {product_data.get('description')}")
            break
    
    if not clueso_product_id:
        logger.error("❌ No Clueso product found!")
        return
    
    # Now find offers for this product
    offers_ref = db.collection("offers")
    offers = offers_ref.where("product_id", "==", clueso_product_id).stream()
    
    offer_count = 0
    active_offers = 0
    
    for offer_doc in offers:
        offer_data = offer_doc.to_dict()
        offer_count += 1
        
        is_active = offer_data.get("active", False)
        if is_active:
            active_offers += 1
        
        logger.info(f"\n📋 Offer {offer_count}:")
        logger.info(f"   ID: {offer_doc.id}")
        logger.info(f"   Active: {is_active}")
        logger.info(f"   Budget: {offer_data.get('budget', 'N/A')}")
        logger.info(f"   Remaining Budget: {offer_data.get('remaining_budget', 'N/A')}")
        logger.info(f"   Status: {offer_data.get('status', 'N/A')}")
        logger.info(f"   Created: {offer_data.get('created_at', 'N/A')}")
        
        # Check if budget is exhausted
        remaining_budget = offer_data.get("remaining_budget", 0)
        if isinstance(remaining_budget, (int, float)) and remaining_budget <= 0:
            logger.info(f"   ⚠️ Budget exhausted!")
    
    logger.info(f"\n📊 Summary:")
    logger.info(f"   Total offers: {offer_count}")
    logger.info(f"   Active offers: {active_offers}")
    
    if active_offers == 0:
        logger.warning("⚠️ No active offers found for Clueso!")
        logger.info("💡 This explains why Clueso didn't appear in recommendations")
    else:
        logger.info("✅ Clueso has active offers - should appear in recommendations")


def check_all_active_offers():
    """Check all active offers to see what's available."""
    logger.info("\n🔍 Checking all active offers...")
    
    offers_ref = db.collection("offers")
    active_offers = offers_ref.where("active", "==", True).stream()
    
    count = 0
    for offer_doc in active_offers:
        offer_data = offer_doc.to_dict()
        product_id = offer_data.get("product_id")
        
        # Get product info
        if product_id:
            product_doc = db.collection("products").document(product_id).get()
            if product_doc.exists:
                product_data = product_doc.to_dict()
                product_title = product_data.get("title", "Unknown")
            else:
                product_title = "Product not found"
        else:
            product_title = "No product ID"
        
        count += 1
        logger.info(f"   {count}. {product_title} (Offer: {offer_doc.id})")
        
        # Check budget
        remaining_budget = offer_data.get("remaining_budget", 0)
        if isinstance(remaining_budget, (int, float)) and remaining_budget <= 0:
            logger.info(f"      ⚠️ Budget exhausted!")
    
    logger.info(f"\n📊 Total active offers: {count}")


def main():
    """Main function."""
    logger.info("🚀 Starting Clueso offers debug...")
    
    try:
        find_clueso_offers()
        check_all_active_offers()
        
    except Exception as e:
        logger.error(f"💥 Debug failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
