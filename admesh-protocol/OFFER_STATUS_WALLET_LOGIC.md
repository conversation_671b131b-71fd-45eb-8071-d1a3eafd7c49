# Offer Status & Wallet Transaction Logic

## Overview

Implemented comprehensive wallet transaction logic for offer status changes in the AdMesh system. The core principle is that `total_budget_allocated` represents actual money allocated to active offers and should never store negative values.

## Status Definitions

### **Active**
- Offer is running and can spend budget
- Money is allocated in `total_budget_allocated`
- Appears in recommendation results

### **Inactive** 
- Offer is temporarily paused
- Money remains allocated in `total_budget_allocated` (no wallet changes)
- Does not appear in recommendation results

### **Cancelled**
- Offer is permanently stopped
- Remaining money is returned to `total_available_balance`
- Cannot spend budget, requires reactivation with new budget

## Wallet Transaction Rules

### ✅ **1. Offer Activation** (Inactive → Active OR Cancelled → Active)

**Money Movement**: `total_available_balance` → `total_budget_allocated`

```python
# Wallet Updates
total_available_balance -= budget_amount
total_budget_allocated += budget_amount

# Transaction Log
type: "debit"
category: "budget_allocation"
description: "Budget allocated for [activated/reactivated] offer: [title]"
```

**Scenarios:**
- **Inactive → Active**: No wallet change (money already allocated)
- **Cancelled → Active**: Wallet allocation required (money moves)

### ✅ **2. Offer Cancellation** (Active → Cancelled OR Inactive → Cancelled)

**Money Movement**: `total_budget_allocated` → `total_available_balance`

```python
# Calculate remaining budget
remaining_budget = offer_total_budget_allocated - offer_total_budget_spent

# Wallet Updates (if remaining_budget > 0)
total_available_balance += remaining_budget
total_budget_allocated -= remaining_budget

# Transaction Log
type: "credit"
category: "budget_refund"
description: "Budget refund from cancelled offer: [title]"
```

### ✅ **3. Status-Only Changes** (Active ↔ Inactive)

**Money Movement**: NONE

```python
# NO wallet changes
# Only update offer status fields
active: true/false
status: "Active"/"Inactive"
updated_at: timestamp

# Create activity log (not wallet transaction)
action: "status_change"
message: "Offer [activated/deactivated] (status change only)"
wallet_transaction: false
```

## Implementation Details

### **Update Offer Function** (`PATCH /offers/{id}`)

Enhanced to handle all status transitions with proper wallet logic:

```python
# Determine transition type
if current_status == "Cancelled" and new_status == "Active":
    # Reactivation: Requires budget allocation
    wallet_operation = "reactivate"
    
elif current_status == "Active" and new_status == "Cancelled":
    # Cancellation: Refund remaining budget
    wallet_operation = "cancel"
    
elif current_status in ["Active", "Inactive"] and new_status in ["Active", "Inactive"]:
    # Status-only change: No wallet movement
    wallet_operation = None
```

### **Cancel Offer Function** (`POST /offers/{id}/cancel`)

Dedicated cancellation endpoint:
- Sets `status: "Cancelled"` and `active: false`
- Calculates and refunds remaining budget
- Removes from active/inactive offer lists
- Creates wallet transaction record

### **Reinstate Offer Function** (`POST /offers/{id}/reinstate`)

Dedicated reactivation endpoint:
- Requires new budget amount in request
- Validates sufficient wallet balance
- Sets `status: "Active"` and `active: true`
- Allocates budget from wallet
- Resets performance metrics
- Creates wallet transaction record

## Validation & Security

### **Budget Validation**
- ✅ `total_budget_allocated` never goes negative
- ✅ Sufficient wallet balance checked before allocation
- ✅ Minimum budget requirements enforced ($1.00)

### **Authorization**
- ✅ Users can only modify their own offers
- ✅ Proper offer ownership verification
- ✅ Status transition validation

### **Error Handling**
- ✅ Missing offers return 404
- ✅ Insufficient balance returns 400 with details
- ✅ Invalid status transitions prevented
- ✅ Database operation failures handled gracefully

## Activity Logging

### **Wallet Transactions**
Created for money movements:
```python
{
    "type": "debit|credit",
    "category": "budget_allocation|budget_refund", 
    "amount": float,
    "description": string,
    "balance_after": float,
    "reference_id": offer_id,
    "reference_type": "offer",
    "timestamp": server_timestamp
}
```

### **Offer Activity Logs**
Created for all status changes:
```python
{
    "offer_id": string,
    "action": "status_change",
    "message": string,
    "previous_status": string,
    "new_status": string,
    "previous_active": boolean,
    "new_active": boolean,
    "wallet_transaction": boolean,
    "timestamp": server_timestamp
}
```

## Test Results

Comprehensive testing confirms all scenarios work correctly:

### ✅ **Status Transition Tests**
1. **Active → Inactive**: No wallet changes ✅
2. **Inactive → Active**: No wallet changes ✅  
3. **Active → Cancelled**: Wallet refund ✅
4. **Cancelled → Active**: Wallet allocation ✅

### ✅ **Wallet Balance Verification**
```
Initial: Available: $200, Allocated: $0
After creation: Available: $150, Allocated: $50
After Active→Inactive: Available: $150, Allocated: $50 (no change)
After Inactive→Active: Available: $150, Allocated: $50 (no change)
After Active→Cancelled: Available: $200, Allocated: $0 (refunded $50)
After Cancelled→Active: Available: $125, Allocated: $75 (allocated $75)
```

### ✅ **Transaction Logging**
- 3 wallet transactions created (allocation + refund + reallocation)
- All activity logs created with correct metadata
- Proper balance calculations in all scenarios

## API Endpoints

### **Update Offer Status**
```http
PATCH /offers/{offer_id}
Content-Type: application/json

{
    "active": true|false,
    "status": "Active"|"Inactive"|"Cancelled",
    "offer_total_budget_allocated": float  // Required for Cancelled→Active
}
```

### **Cancel Offer**
```http
POST /offers/{offer_id}/cancel
```

### **Reinstate Offer**
```http
POST /offers/{offer_id}/reinstate
Content-Type: application/json

{
    "budget_amount": float  // New budget to allocate
}
```

## Benefits

1. **Financial Accuracy**: Proper tracking of allocated vs available funds
2. **Audit Trail**: Complete transaction history for all money movements
3. **Flexibility**: Support for temporary pausing (inactive) vs permanent stopping (cancelled)
4. **Security**: Prevents negative balances and unauthorized access
5. **Clarity**: Clear distinction between status changes that affect money vs those that don't

The implementation ensures that brands have accurate financial tracking and reliable budget operations across all offer lifecycle states.
