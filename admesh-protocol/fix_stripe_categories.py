#!/usr/bin/env python3
"""
Fix Stripe offer categories to include 'payments'
"""
import sys
import os
sys.path.append('.')

from firebase.config import get_db

def fix_stripe_categories():
    db = get_db()
    
    print("🔧 Fixing Stripe categories...")
    
    # Find Stripe offer
    offers = db.collection("offers").where("title", "==", "Stripe").stream()
    
    for doc in offers:
        offer_id = doc.id
        offer = doc.to_dict()
        
        print(f"Found Stripe offer: {offer_id}")
        print(f"Current categories: {offer.get('categories')}")
        
        # Update categories to include 'payments'
        current_categories = offer.get('categories', [])
        if 'payments' not in current_categories:
            new_categories = current_categories + ['payments']
            
            # Update offer
            db.collection("offers").document(offer_id).update({
                "categories": new_categories
            })
            print(f"✅ Updated offer categories to: {new_categories}")
            
            # Also update the product
            product_id = offer.get('product_id')
            if product_id:
                product_doc = db.collection("products").document(product_id).get()
                if product_doc.exists:
                    product = product_doc.to_dict()
                    product_categories = product.get('categories', [])
                    if 'payments' not in product_categories:
                        new_product_categories = product_categories + ['payments']
                        db.collection("products").document(product_id).update({
                            "categories": new_product_categories
                        })
                        print(f"✅ Updated product categories to: {new_product_categories}")
                        
                        # Regenerate embedding with new categories
                        from api.utils.embedding import embed_text
                        
                        # Create comprehensive text for embedding
                        embedding_text_parts = []
                        
                        if product.get("title"):
                            embedding_text_parts.append(f"Product: {product['title']}")
                        if product.get("description"):
                            embedding_text_parts.append(f"Description: {product['description']}")
                        if new_product_categories:
                            categories_text = ", ".join(new_product_categories)
                            embedding_text_parts.append(f"Categories: {categories_text}")
                        if product.get("keywords"):
                            keywords_text = ", ".join(product["keywords"])
                            embedding_text_parts.append(f"Keywords: {keywords_text}")
                        if product.get("audience_segment"):
                            embedding_text_parts.append(f"Target audience: {product['audience_segment']}")
                        if product.get("integration_list"):
                            integrations_text = ", ".join(product["integration_list"])
                            embedding_text_parts.append(f"Integrations: {integrations_text}")
                        
                        embedding_text = ". ".join(embedding_text_parts)
                        
                        try:
                            product_embedding = embed_text(embedding_text)

                            # Update in the new product_embeddings collection
                            from api.utils.embedding import update_product_embedding_in_collection

                            metadata = {
                                "source_text": embedding_text,
                                "generation_method": "openai_text-embedding-3-small",
                                "updated_during": "stripe_categories_fix",
                                "updated_fields": ["categories"]
                            }

                            success = update_product_embedding_in_collection(product_id, product_embedding, metadata)
                            if success:
                                print(f"✅ Regenerated and updated embedding for updated product in new collection")
                            else:
                                print(f"⚠️ Generated embedding but failed to update in new collection")

                        except Exception as e:
                            print(f"❌ Failed to regenerate embedding: {e}")
        else:
            print("✅ 'payments' category already exists")

if __name__ == "__main__":
    fix_stripe_categories()
