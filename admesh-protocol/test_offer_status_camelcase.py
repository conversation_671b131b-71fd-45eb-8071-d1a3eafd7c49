#!/usr/bin/env python3
"""
Test script to verify offer status handling with camelCase and proper button behavior.

This script tests:
1. Status storage and retrieval uses camelCase (Active, Inactive, Cancelled)
2. Cancelled offers show "Reactivate" button (money movement required)
3. Inactive offers show "Activate" button (no money movement)
4. Active offers show "Deactivate" button (no money movement)

Usage:
    python test_offer_status_camelcase.py
"""

import sys
import os
import logging
from typing import Dict, Any
import uuid

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class OfferStatusCamelCaseTester:
    """Test suite for offer status camelCase handling."""
    
    def __init__(self):
        self.test_brand_id = "test_brand_camelcase"
        self.test_product_id = "test_product_camelcase"
        self.test_offer_id = None
        self.initial_wallet_balance = 500.0  # $500 initial balance
        self.test_budget_amount = 100.0  # $100 test budget
        
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete test offer
            if self.test_offer_id:
                db.collection("offers").document(self.test_offer_id).delete()
            
            # Delete test product
            db.collection("products").document(self.test_product_id).delete()
            
            # Delete test wallet and transactions
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            
            # Delete all transactions
            transactions = wallet_ref.collection("transactions").stream()
            for tx in transactions:
                tx.reference.delete()
            
            # Delete wallet
            wallet_ref.delete()
            
            # Delete test brand
            db.collection("brands").document(self.test_brand_id).delete()
            
        except Exception as e:
            logger.debug(f"Cleanup error (expected): {e}")
        
        logger.info("✅ Cleanup completed")
    
    def setup_test_data(self):
        """Set up test brand, product, and wallet."""
        logger.info("🔧 Setting up test data...")
        
        # Create test brand
        brand_data = {
            "brand_id": self.test_brand_id,
            "company_name": "Test CamelCase Company",
            "website": "https://test-camelcase.com",
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("brands").document(self.test_brand_id).set(brand_data)
        
        # Create test product
        product_data = {
            "id": self.test_product_id,
            "title": "Test CamelCase Product",
            "description": "A test product for camelCase testing",
            "brand_id": self.test_brand_id,
            "categories": ["testing"],
            "keywords": ["test", "camelcase"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("products").document(self.test_product_id).set(product_data)
        
        # Create test wallet with initial balance
        wallet_data = {
            "brand_id": self.test_brand_id,
            "total_available_balance": self.initial_wallet_balance,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("wallets").document(self.test_brand_id).set(wallet_data)
        
        logger.info(f"✅ Test data setup complete. Initial wallet balance: ${self.initial_wallet_balance}")
    
    def create_active_offer(self) -> str:
        """Create an active test offer."""
        logger.info(f"📝 Creating active test offer with ${self.test_budget_amount} budget...")
        
        self.test_offer_id = str(uuid.uuid4())
        
        # Create offer
        offer_data = {
            "id": self.test_offer_id,
            "brand_id": self.test_brand_id,
            "product_id": self.test_product_id,
            "offer_title": "Test CamelCase Offer",
            "offer_description": "Test offer for camelCase testing",
            "active": True,
            "status": "Active",  # Store in camelCase
            "offer_total_budget_allocated": self.test_budget_amount,
            "offer_total_budget_spent": 0.0,
            "offer_total_promo_spent": 0.0,
            "remaining_budget": self.test_budget_amount,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("offers").document(self.test_offer_id).set(offer_data)
        
        # Update wallet to allocate budget
        wallet_ref = db.collection("wallets").document(self.test_brand_id)
        wallet_ref.update({
            "total_available_balance": firestore.Increment(-self.test_budget_amount),
            "total_budget_allocated": firestore.Increment(self.test_budget_amount),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        logger.info(f"✅ Active test offer created: {self.test_offer_id}")
        return self.test_offer_id
    
    def get_offer_data(self) -> Dict[str, Any]:
        """Get current offer data."""
        offer_doc = db.collection("offers").document(self.test_offer_id).get()
        if offer_doc.exists:
            return offer_doc.to_dict()
        return {}
    
    def simulate_offer_retrieval(self) -> Dict[str, Any]:
        """Simulate the offer retrieval logic from get_all_brand_offers."""
        offer_data = self.get_offer_data()
        
        # Simulate the status logic from the API
        status = offer_data.get("status", "Active" if offer_data.get("active", False) else "Inactive")
        active = offer_data.get("active", False)
        
        return {
            "id": self.test_offer_id,
            "status": status,
            "active": active,
            "offer_title": offer_data.get("offer_title", ""),
            "offer_total_budget_allocated": offer_data.get("offer_total_budget_allocated", 0.0),
            "remaining_budget": offer_data.get("remaining_budget", 0.0)
        }
    
    def determine_button_action(self, status: str, active: bool) -> str:
        """Determine what button should be shown based on status."""
        if status == "Cancelled":
            return "Reactivate"  # Money movement required
        elif status == "Active" and active:
            return "Deactivate"  # No money movement
        elif status == "Inactive" and not active:
            return "Activate"  # No money movement
        else:
            return "Unknown"
    
    def test_active_status(self) -> bool:
        """Test Active status handling."""
        logger.info("🧪 Testing Active status...")
        
        try:
            # Create active offer
            self.create_active_offer()
            
            # Retrieve and check status
            retrieved_offer = self.simulate_offer_retrieval()
            
            if retrieved_offer["status"] == "Active" and retrieved_offer["active"]:
                logger.info("   ✅ Active status stored and retrieved correctly")
                
                button_action = self.determine_button_action(retrieved_offer["status"], retrieved_offer["active"])
                if button_action == "Deactivate":
                    logger.info("   ✅ Correct button action: Deactivate (no money movement)")
                    return True
                else:
                    logger.error(f"   ❌ Wrong button action: {button_action}")
                    return False
            else:
                logger.error(f"   ❌ Wrong status: {retrieved_offer['status']}, active: {retrieved_offer['active']}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Active status test failed: {e}")
            return False
    
    def test_inactive_status(self) -> bool:
        """Test Inactive status handling."""
        logger.info("🧪 Testing Inactive status...")
        
        try:
            # Update offer to inactive
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": False,
                "status": "Inactive",  # Store in camelCase
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Retrieve and check status
            retrieved_offer = self.simulate_offer_retrieval()
            
            if retrieved_offer["status"] == "Inactive" and not retrieved_offer["active"]:
                logger.info("   ✅ Inactive status stored and retrieved correctly")
                
                button_action = self.determine_button_action(retrieved_offer["status"], retrieved_offer["active"])
                if button_action == "Activate":
                    logger.info("   ✅ Correct button action: Activate (no money movement)")
                    return True
                else:
                    logger.error(f"   ❌ Wrong button action: {button_action}")
                    return False
            else:
                logger.error(f"   ❌ Wrong status: {retrieved_offer['status']}, active: {retrieved_offer['active']}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Inactive status test failed: {e}")
            return False
    
    def test_cancelled_status(self) -> bool:
        """Test Cancelled status handling."""
        logger.info("🧪 Testing Cancelled status...")
        
        try:
            # Update offer to cancelled
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": False,
                "status": "Cancelled",  # Store in camelCase
                "cancelled_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Retrieve and check status
            retrieved_offer = self.simulate_offer_retrieval()
            
            if retrieved_offer["status"] == "Cancelled" and not retrieved_offer["active"]:
                logger.info("   ✅ Cancelled status stored and retrieved correctly")
                
                button_action = self.determine_button_action(retrieved_offer["status"], retrieved_offer["active"])
                if button_action == "Reactivate":
                    logger.info("   ✅ Correct button action: Reactivate (money movement required)")
                    return True
                else:
                    logger.error(f"   ❌ Wrong button action: {button_action}")
                    return False
            else:
                logger.error(f"   ❌ Wrong status: {retrieved_offer['status']}, active: {retrieved_offer['active']}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Cancelled status test failed: {e}")
            return False
    
    def run_camelcase_tests(self) -> bool:
        """Run all camelCase status tests."""
        logger.info("🚀 Starting camelCase status tests...")
        
        try:
            # Setup
            self.cleanup()
            self.setup_test_data()
            
            tests = [
                ("Active Status", self.test_active_status),
                ("Inactive Status", self.test_inactive_status),
                ("Cancelled Status", self.test_cancelled_status)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"📋 Running test: {test_name}")
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
            
            # Final results
            logger.info("=" * 60)
            logger.info("📊 CAMELCASE STATUS TESTS RESULTS")
            logger.info("=" * 60)
            logger.info(f"Tests passed: {passed}/{total}")
            logger.info("Status handling:")
            logger.info("  - Active: Deactivate button (no money movement)")
            logger.info("  - Inactive: Activate button (no money movement)")
            logger.info("  - Cancelled: Reactivate button (money movement required)")
            logger.info("=" * 60)
            
            if passed == total:
                logger.info("🎉 All camelCase status tests PASSED!")
                return True
            else:
                logger.error(f"💥 {total - passed} tests FAILED!")
                return False
                
        except Exception as e:
            logger.error(f"💥 CamelCase test suite failed: {e}")
            return False
        finally:
            # Cleanup
            self.cleanup()


def main():
    """Main function to run the camelCase tests."""
    tester = OfferStatusCamelCaseTester()
    
    try:
        success = tester.run_camelcase_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
