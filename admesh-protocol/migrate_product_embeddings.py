#!/usr/bin/env python3
"""
Migration script to move product embeddings from the products collection 
to a dedicated product_embeddings collection.

This script:
1. Scans all products in the products collection
2. Extracts embeddings and metadata
3. Creates corresponding documents in the product_embeddings collection
4. Optionally removes embeddings from the products collection after successful migration
5. Provides detailed logging and progress tracking

Usage:
    python migrate_product_embeddings.py [--dry-run] [--remove-from-products] [--batch-size=50]
"""

import sys
import os
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore
from api.utils.embedding import (
    store_product_embedding_in_collection,
    get_product_embedding_from_collection,
    generate_embedding_text_from_product
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

db = get_db()


class ProductEmbeddingMigrator:
    """Handles the migration of product embeddings to the new collection structure."""
    
    def __init__(self, dry_run: bool = False, remove_from_products: bool = False, batch_size: int = 50):
        self.dry_run = dry_run
        self.remove_from_products = remove_from_products
        self.batch_size = batch_size
        self.stats = {
            'total_products': 0,
            'products_with_embeddings': 0,
            'successful_migrations': 0,
            'failed_migrations': 0,
            'already_migrated': 0,
            'removed_from_products': 0
        }
    
    def migrate_all_embeddings(self) -> Dict[str, int]:
        """
        Migrate all product embeddings from products to product_embeddings collection.
        
        Returns:
            Dictionary with migration statistics
        """
        logger.info("🚀 Starting product embeddings migration...")
        logger.info(f"📊 Configuration: dry_run={self.dry_run}, remove_from_products={self.remove_from_products}, batch_size={self.batch_size}")
        
        if self.dry_run:
            logger.info("🔍 DRY RUN MODE - No changes will be made")
        
        try:
            # Get all products in batches
            products_ref = db.collection("products")
            
            # Process products in batches
            last_doc = None
            batch_count = 0
            
            while True:
                batch_count += 1
                logger.info(f"📦 Processing batch {batch_count}...")
                
                # Build query
                query = products_ref.limit(self.batch_size)
                if last_doc:
                    query = query.start_after(last_doc)
                
                # Get batch of products
                products = list(query.stream())
                
                if not products:
                    logger.info("✅ No more products to process")
                    break
                
                # Process each product in the batch
                for product_doc in products:
                    self._migrate_single_product(product_doc)
                    last_doc = product_doc
                
                logger.info(f"📊 Batch {batch_count} completed. Progress: {self.stats['successful_migrations']} successful, {self.stats['failed_migrations']} failed")
            
            # Final statistics
            self._log_final_stats()
            return self.stats
            
        except Exception as e:
            logger.error(f"❌ Migration failed with error: {str(e)}")
            raise
    
    def _migrate_single_product(self, product_doc) -> None:
        """Migrate a single product's embedding."""
        try:
            product_id = product_doc.id
            product_data = product_doc.to_dict()
            
            self.stats['total_products'] += 1
            
            # Check if product has an embedding
            existing_embedding = product_data.get("embedding")
            if not existing_embedding or not isinstance(existing_embedding, list) or len(existing_embedding) == 0:
                logger.debug(f"⏭️ Product {product_id} has no embedding, skipping")
                return
            
            self.stats['products_with_embeddings'] += 1
            
            # Check if already migrated
            if not self.dry_run:
                existing_in_new_collection = get_product_embedding_from_collection(product_id)
                if existing_in_new_collection:
                    logger.debug(f"✅ Product {product_id} already migrated, skipping")
                    self.stats['already_migrated'] += 1
                    return
            
            # Prepare metadata
            metadata = {
                "source_text": generate_embedding_text_from_product(product_data),
                "generation_method": "migrated_from_products_collection",
                "original_embedding_updated_at": product_data.get("embedding_updated_at"),
                "migration_date": firestore.SERVER_TIMESTAMP
            }
            
            # Migrate the embedding
            if not self.dry_run:
                success = store_product_embedding_in_collection(product_id, existing_embedding, metadata)
                if success:
                    self.stats['successful_migrations'] += 1
                    logger.info(f"✅ Migrated embedding for product {product_id} ({product_data.get('title', 'No Title')})")
                    
                    # Optionally remove from products collection
                    if self.remove_from_products:
                        self._remove_embedding_from_product(product_id)
                else:
                    self.stats['failed_migrations'] += 1
                    logger.error(f"❌ Failed to migrate embedding for product {product_id}")
            else:
                logger.info(f"🔍 [DRY RUN] Would migrate embedding for product {product_id} ({product_data.get('title', 'No Title')})")
                self.stats['successful_migrations'] += 1
                
        except Exception as e:
            self.stats['failed_migrations'] += 1
            logger.error(f"❌ Error migrating product {product_doc.id}: {str(e)}")
    
    def _remove_embedding_from_product(self, product_id: str) -> None:
        """Remove embedding field from product document."""
        try:
            db.collection("products").document(product_id).update({
                "embedding": firestore.DELETE_FIELD,
                "embedding_updated_at": firestore.DELETE_FIELD
            })
            self.stats['removed_from_products'] += 1
            logger.debug(f"🗑️ Removed embedding from product {product_id}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to remove embedding from product {product_id}: {str(e)}")
    
    def _log_final_stats(self) -> None:
        """Log final migration statistics."""
        logger.info("=" * 60)
        logger.info("📊 MIGRATION COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Total products processed: {self.stats['total_products']}")
        logger.info(f"Products with embeddings: {self.stats['products_with_embeddings']}")
        logger.info(f"Successful migrations: {self.stats['successful_migrations']}")
        logger.info(f"Failed migrations: {self.stats['failed_migrations']}")
        logger.info(f"Already migrated: {self.stats['already_migrated']}")
        if self.remove_from_products:
            logger.info(f"Removed from products collection: {self.stats['removed_from_products']}")
        logger.info("=" * 60)


def main():
    """Main function to run the migration."""
    parser = argparse.ArgumentParser(description="Migrate product embeddings to dedicated collection")
    parser.add_argument("--dry-run", action="store_true", help="Run in dry-run mode (no changes made)")
    parser.add_argument("--remove-from-products", action="store_true", help="Remove embeddings from products collection after migration")
    parser.add_argument("--batch-size", type=int, default=50, help="Number of products to process in each batch")
    
    args = parser.parse_args()
    
    # Create migrator and run migration
    migrator = ProductEmbeddingMigrator(
        dry_run=args.dry_run,
        remove_from_products=args.remove_from_products,
        batch_size=args.batch_size
    )
    
    try:
        stats = migrator.migrate_all_embeddings()
        
        if stats['failed_migrations'] > 0:
            logger.warning(f"⚠️ Migration completed with {stats['failed_migrations']} failures")
            sys.exit(1)
        else:
            logger.info("🎉 Migration completed successfully!")
            sys.exit(0)
            
    except Exception as e:
        logger.error(f"💥 Migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
