#!/usr/bin/env python3
"""
Test script to verify all offer status transition scenarios and wallet transaction logic.

This script tests:
1. Active → Inactive (no wallet change)
2. Inactive → Active (no wallet change)
3. Active → Cancelled (wallet refund)
4. Cancelled → Active (wallet allocation)
5. Inactive → Cancelled (wallet refund)
6. Cancelled → Inactive (wallet allocation)

Usage:
    python test_offer_status_transitions.py
"""

import sys
import os
import logging
from typing import Dict, Any
import uuid

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class OfferStatusTransitionTester:
    """Test suite for offer status transitions and wallet logic."""
    
    def __init__(self):
        self.test_brand_id = "test_brand_status_transitions"
        self.test_product_id = "test_product_status_transitions"
        self.test_offer_id = None
        self.initial_wallet_balance = 200.0  # $200 initial balance
        self.test_budget_amount = 50.0  # $50 test budget
        
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete test offer
            if self.test_offer_id:
                db.collection("offers").document(self.test_offer_id).delete()
            
            # Delete test product
            db.collection("products").document(self.test_product_id).delete()
            
            # Delete test wallet and transactions
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            
            # Delete all transactions
            transactions = wallet_ref.collection("transactions").stream()
            for tx in transactions:
                tx.reference.delete()
            
            # Delete wallet
            wallet_ref.delete()
            
            # Delete test brand
            db.collection("brands").document(self.test_brand_id).delete()
            
        except Exception as e:
            logger.debug(f"Cleanup error (expected): {e}")
        
        logger.info("✅ Cleanup completed")
    
    def setup_test_data(self):
        """Set up test brand, product, and wallet."""
        logger.info("🔧 Setting up test data...")
        
        # Create test brand
        brand_data = {
            "brand_id": self.test_brand_id,
            "company_name": "Test Status Transitions Company",
            "website": "https://test-status-transitions.com",
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("brands").document(self.test_brand_id).set(brand_data)
        
        # Create test product
        product_data = {
            "id": self.test_product_id,
            "title": "Test Status Transitions Product",
            "description": "A test product for status transition testing",
            "brand_id": self.test_brand_id,
            "categories": ["testing"],
            "keywords": ["test", "status", "transitions"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("products").document(self.test_product_id).set(product_data)
        
        # Create test wallet with initial balance
        wallet_data = {
            "brand_id": self.test_brand_id,
            "total_available_balance": self.initial_wallet_balance,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("wallets").document(self.test_brand_id).set(wallet_data)
        
        logger.info(f"✅ Test data setup complete. Initial wallet balance: ${self.initial_wallet_balance}")
    
    def create_active_offer(self) -> str:
        """Create an active test offer with budget allocation."""
        logger.info(f"📝 Creating active test offer with ${self.test_budget_amount} budget...")
        
        self.test_offer_id = str(uuid.uuid4())
        
        # Create offer
        offer_data = {
            "id": self.test_offer_id,
            "brand_id": self.test_brand_id,
            "product_id": self.test_product_id,
            "offer_title": "Test Status Transitions Offer",
            "offer_description": "Test offer for status transition testing",
            "active": True,
            "status": "Active",
            "offer_total_budget_allocated": self.test_budget_amount,
            "offer_total_budget_spent": 0.0,
            "offer_total_promo_spent": 0.0,
            "remaining_budget": self.test_budget_amount,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("offers").document(self.test_offer_id).set(offer_data)
        
        # Update wallet to allocate budget
        wallet_ref = db.collection("wallets").document(self.test_brand_id)
        wallet_ref.update({
            "total_available_balance": firestore.Increment(-self.test_budget_amount),
            "total_budget_allocated": firestore.Increment(self.test_budget_amount),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        # Create transaction record
        from api.routes.brands import create_wallet_transaction
        create_wallet_transaction(
            brand_id=self.test_brand_id,
            transaction_type="debit",
            category="budget_allocation",
            amount=self.test_budget_amount,
            description=f"Budget allocated for test offer: {offer_data['offer_title']}",
            balance_after=self.initial_wallet_balance - self.test_budget_amount,
            reference_id=self.test_offer_id,
            reference_type="offer"
        )
        
        logger.info(f"✅ Active test offer created: {self.test_offer_id}")
        return self.test_offer_id
    
    def get_wallet_balance(self) -> Dict[str, float]:
        """Get current wallet balance."""
        wallet_doc = db.collection("wallets").document(self.test_brand_id).get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            return {
                "available": wallet_data.get("total_available_balance", 0.0),
                "allocated": wallet_data.get("total_budget_allocated", 0.0),
                "spent": wallet_data.get("total_balance_spent", 0.0)
            }
        return {"available": 0.0, "allocated": 0.0, "spent": 0.0}
    
    def get_offer_status(self) -> Dict[str, Any]:
        """Get current offer status."""
        offer_doc = db.collection("offers").document(self.test_offer_id).get()
        if offer_doc.exists:
            offer_data = offer_doc.to_dict()
            return {
                "active": offer_data.get("active", False),
                "status": offer_data.get("status", "Unknown"),
                "budget_allocated": offer_data.get("offer_total_budget_allocated", 0.0),
                "budget_spent": offer_data.get("offer_total_budget_spent", 0.0),
                "remaining_budget": offer_data.get("remaining_budget", 0.0)
            }
        return {}
    
    def get_transaction_count(self) -> int:
        """Get number of wallet transactions."""
        transactions = db.collection("wallets").document(self.test_brand_id).collection("transactions").stream()
        return len(list(transactions))
    
    def test_active_to_inactive(self) -> bool:
        """Test Active → Inactive transition (no wallet change)."""
        logger.info("🧪 Testing Active → Inactive transition...")
        
        try:
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            
            # Update offer to inactive
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": False,
                "status": "Inactive",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            offer_status = self.get_offer_status()
            
            # Verify no wallet changes
            if (balance_before["available"] == balance_after["available"] and 
                balance_before["allocated"] == balance_after["allocated"] and
                tx_count_before == tx_count_after):
                logger.info("   ✅ No wallet changes (correct)")
            else:
                logger.error("   ❌ Unexpected wallet changes")
                return False
            
            # Verify offer status
            if not offer_status["active"] and offer_status["status"] == "Inactive":
                logger.info("   ✅ Offer status correctly updated")
            else:
                logger.error("   ❌ Offer status incorrect")
                return False
            
            logger.info("✅ Active → Inactive test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Active → Inactive test failed: {e}")
            return False
    
    def test_inactive_to_active(self) -> bool:
        """Test Inactive → Active transition (no wallet change)."""
        logger.info("🧪 Testing Inactive → Active transition...")
        
        try:
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            
            # Update offer to active
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": True,
                "status": "Active",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            offer_status = self.get_offer_status()
            
            # Verify no wallet changes
            if (balance_before["available"] == balance_after["available"] and 
                balance_before["allocated"] == balance_after["allocated"] and
                tx_count_before == tx_count_after):
                logger.info("   ✅ No wallet changes (correct)")
            else:
                logger.error("   ❌ Unexpected wallet changes")
                return False
            
            # Verify offer status
            if offer_status["active"] and offer_status["status"] == "Active":
                logger.info("   ✅ Offer status correctly updated")
            else:
                logger.error("   ❌ Offer status incorrect")
                return False
            
            logger.info("✅ Inactive → Active test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Inactive → Active test failed: {e}")
            return False
    
    def test_active_to_cancelled(self) -> bool:
        """Test Active → Cancelled transition (wallet refund)."""
        logger.info("🧪 Testing Active → Cancelled transition...")
        
        try:
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            offer_before = self.get_offer_status()
            
            remaining_budget = offer_before["remaining_budget"]
            
            # Cancel the offer
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": False,
                "status": "Cancelled",
                "cancelled_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Simulate wallet refund (this would normally be done by the cancel endpoint)
            if remaining_budget > 0:
                wallet_ref = db.collection("wallets").document(self.test_brand_id)
                wallet_ref.update({
                    "total_available_balance": firestore.Increment(remaining_budget),
                    "total_budget_allocated": firestore.Increment(-remaining_budget),
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                
                # Create transaction record
                from api.routes.brands import create_wallet_transaction
                create_wallet_transaction(
                    brand_id=self.test_brand_id,
                    transaction_type="credit",
                    category="budget_refund",
                    amount=remaining_budget,
                    description=f"Budget refund from cancelled offer: Test Status Transitions Offer",
                    balance_after=balance_before["available"] + remaining_budget,
                    reference_id=self.test_offer_id,
                    reference_type="offer"
                )
            
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            offer_status = self.get_offer_status()
            
            # Verify wallet changes
            expected_available = balance_before["available"] + remaining_budget
            expected_allocated = balance_before["allocated"] - remaining_budget
            
            if (abs(balance_after["available"] - expected_available) < 0.01 and 
                abs(balance_after["allocated"] - expected_allocated) < 0.01 and
                tx_count_after == tx_count_before + 1):
                logger.info(f"   ✅ Wallet correctly refunded ${remaining_budget}")
            else:
                logger.error("   ❌ Wallet refund incorrect")
                return False
            
            # Verify offer status
            if not offer_status["active"] and offer_status["status"] == "Cancelled":
                logger.info("   ✅ Offer status correctly updated")
            else:
                logger.error("   ❌ Offer status incorrect")
                return False
            
            logger.info("✅ Active → Cancelled test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Active → Cancelled test failed: {e}")
            return False
    
    def test_cancelled_to_active(self) -> bool:
        """Test Cancelled → Active transition (wallet allocation)."""
        logger.info("🧪 Testing Cancelled → Active transition...")
        
        try:
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            
            new_budget = 75.0  # $75 for reactivation
            
            # Check sufficient balance
            if balance_before["available"] < new_budget:
                logger.error(f"   ❌ Insufficient balance for test")
                return False
            
            # Reactivate the offer
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_ref.update({
                "active": True,
                "status": "Active",
                "offer_total_budget_allocated": new_budget,
                "offer_total_budget_spent": 0.0,
                "offer_total_promo_spent": 0.0,
                "remaining_budget": new_budget,
                "reactivated_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Simulate wallet allocation (this would normally be done by the reinstate endpoint)
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            wallet_ref.update({
                "total_available_balance": firestore.Increment(-new_budget),
                "total_budget_allocated": firestore.Increment(new_budget),
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Create transaction record
            from api.routes.brands import create_wallet_transaction
            create_wallet_transaction(
                brand_id=self.test_brand_id,
                transaction_type="debit",
                category="budget_allocation",
                amount=new_budget,
                description=f"Budget allocated for reactivated offer: Test Status Transitions Offer",
                balance_after=balance_before["available"] - new_budget,
                reference_id=self.test_offer_id,
                reference_type="offer"
            )
            
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            offer_status = self.get_offer_status()
            
            # Verify wallet changes
            expected_available = balance_before["available"] - new_budget
            expected_allocated = balance_before["allocated"] + new_budget
            
            if (abs(balance_after["available"] - expected_available) < 0.01 and 
                abs(balance_after["allocated"] - expected_allocated) < 0.01 and
                tx_count_after == tx_count_before + 1):
                logger.info(f"   ✅ Wallet correctly allocated ${new_budget}")
            else:
                logger.error("   ❌ Wallet allocation incorrect")
                return False
            
            # Verify offer status
            if offer_status["active"] and offer_status["status"] == "Active":
                logger.info("   ✅ Offer status correctly updated")
            else:
                logger.error("   ❌ Offer status incorrect")
                return False
            
            logger.info("✅ Cancelled → Active test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Cancelled → Active test failed: {e}")
            return False
    
    def run_all_transition_tests(self) -> bool:
        """Run all status transition tests."""
        logger.info("🚀 Starting offer status transition tests...")
        
        try:
            # Setup
            self.cleanup()
            self.setup_test_data()
            self.create_active_offer()
            
            tests = [
                ("Active → Inactive", self.test_active_to_inactive),
                ("Inactive → Active", self.test_inactive_to_active),
                ("Active → Cancelled", self.test_active_to_cancelled),
                ("Cancelled → Active", self.test_cancelled_to_active)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"📋 Running test: {test_name}")
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
            
            # Final results
            final_balance = self.get_wallet_balance()
            final_tx_count = self.get_transaction_count()
            
            logger.info("=" * 60)
            logger.info("📊 FINAL RESULTS")
            logger.info("=" * 60)
            logger.info(f"Tests passed: {passed}/{total}")
            logger.info(f"Initial wallet balance: ${self.initial_wallet_balance}")
            logger.info(f"Final available balance: ${final_balance['available']}")
            logger.info(f"Final allocated balance: ${final_balance['allocated']}")
            logger.info(f"Total transactions created: {final_tx_count}")
            logger.info("=" * 60)
            
            if passed == total:
                logger.info("🎉 All status transition tests PASSED!")
                return True
            else:
                logger.error(f"💥 {total - passed} tests FAILED!")
                return False
                
        except Exception as e:
            logger.error(f"💥 Test suite failed: {e}")
            return False
        finally:
            # Cleanup
            self.cleanup()


def main():
    """Main function to run the tests."""
    tester = OfferStatusTransitionTester()
    
    try:
        success = tester.run_all_transition_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
