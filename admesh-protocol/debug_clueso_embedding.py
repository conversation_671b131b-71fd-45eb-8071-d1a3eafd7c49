#!/usr/bin/env python3
"""
Debug script to check Clueso product embedding and similarity with "best link shortner" query.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from api.utils.embedding import (
    embed_text,
    cosine_similarity,
    get_product_embedding,
    generate_embedding_text_from_product
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


def find_clueso_product():
    """Find Clueso product in the database."""
    logger.info("🔍 Searching for Clueso product...")
    
    products_ref = db.collection("products")
    
    # Search by title containing "clueso" (case insensitive)
    products = products_ref.stream()
    
    clueso_products = []
    for product_doc in products:
        product_data = product_doc.to_dict()
        title = product_data.get("title", "").lower()
        description = product_data.get("description", "").lower()
        
        if "clueso" in title or "clueso" in description:
            clueso_products.append({
                "id": product_doc.id,
                "data": product_data
            })
    
    return clueso_products


def test_embedding_similarity():
    """Test embedding similarity between query and Clueso product."""
    logger.info("🧪 Testing embedding similarity...")
    
    # Find Clueso products
    clueso_products = find_clueso_product()
    
    if not clueso_products:
        logger.error("❌ No Clueso products found!")
        return
    
    logger.info(f"✅ Found {len(clueso_products)} Clueso product(s)")
    
    # Test query
    test_query = "best link shortner"
    logger.info(f"🔍 Testing query: '{test_query}'")
    
    # Generate query embedding
    query_embedding = embed_text(test_query)
    logger.info(f"✅ Generated query embedding with {len(query_embedding)} dimensions")
    
    # Test each Clueso product
    for i, clueso in enumerate(clueso_products):
        product_id = clueso["id"]
        product_data = clueso["data"]
        
        logger.info(f"\n📋 Testing Clueso Product {i+1}:")
        logger.info(f"   ID: {product_id}")
        logger.info(f"   Title: {product_data.get('title', 'N/A')}")
        logger.info(f"   Description: {product_data.get('description', 'N/A')}")
        logger.info(f"   Categories: {product_data.get('categories', [])}")
        logger.info(f"   Keywords: {product_data.get('keywords', [])}")
        
        # Generate embedding text
        embedding_text = generate_embedding_text_from_product(product_data)
        logger.info(f"   Embedding text: {embedding_text}")
        
        # Get product embedding
        product_embedding = get_product_embedding(product_id, product_data)
        logger.info(f"   Product embedding dimensions: {len(product_embedding)}")
        
        # Calculate similarity
        similarity = cosine_similarity(query_embedding, product_embedding)
        logger.info(f"   🎯 Similarity score: {similarity:.4f}")
        
        # Check against threshold
        threshold = 0.3
        if similarity >= threshold:
            logger.info(f"   ✅ PASS: Similarity {similarity:.4f} >= threshold {threshold}")
        else:
            logger.info(f"   ❌ FAIL: Similarity {similarity:.4f} < threshold {threshold}")
        
        # Test with corrected spelling
        corrected_query = "best link shortener"
        corrected_embedding = embed_text(corrected_query)
        corrected_similarity = cosine_similarity(corrected_embedding, product_embedding)
        logger.info(f"   🔄 With corrected spelling '{corrected_query}': {corrected_similarity:.4f}")
        
        # Test with just "link shortener"
        simple_query = "link shortener"
        simple_embedding = embed_text(simple_query)
        simple_similarity = cosine_similarity(simple_embedding, product_embedding)
        logger.info(f"   🔄 With simple query '{simple_query}': {simple_similarity:.4f}")


def suggest_improvements():
    """Suggest improvements based on findings."""
    logger.info("\n💡 SUGGESTIONS:")
    logger.info("1. Consider lowering the semantic similarity threshold from 0.3 to 0.25")
    logger.info("2. Implement query preprocessing to fix common misspellings")
    logger.info("3. Add synonym expansion (shortner -> shortener)")
    logger.info("4. Consider using fuzzy matching for keywords")
    logger.info("5. Add more descriptive keywords to product data")


def main():
    """Main function."""
    logger.info("🚀 Starting Clueso embedding debug...")
    
    try:
        test_embedding_similarity()
        suggest_improvements()
        
    except Exception as e:
        logger.error(f"💥 Debug failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
