#!/usr/bin/env python3
"""
Debug script to check Stripe offer data and why it's not matching
"""
import sys
import os
sys.path.append('.')

from firebase.config import get_db

def debug_stripe_offers():
    db = get_db()
    
    print("🔍 Debugging Stripe offers...")
    print("=" * 50)
    
    # 1. Check all active offers
    print("\n1. All active offers:")
    active_offers = db.collection("offers").where("offer_status", "==", "active").stream()
    active_count = 0
    stripe_offers = []
    
    for doc in active_offers:
        active_count += 1
        offer = doc.to_dict()
        title = offer.get("title", "No Title")
        categories = offer.get("categories", [])
        
        print(f"   - {title}: categories={categories}")
        
        if "stripe" in title.lower():
            stripe_offers.append((doc.id, offer))
    
    print(f"\nTotal active offers: {active_count}")
    print(f"Stripe offers found: {len(stripe_offers)}")
    
    # 2. Check Stripe offers specifically
    if stripe_offers:
        print("\n2. Stripe offer details:")
        for offer_id, offer in stripe_offers:
            print(f"\nOffer ID: {offer_id}")
            print(f"Title: {offer.get('title')}")
            print(f"Categories: {offer.get('categories')}")
            print(f"Keywords: {offer.get('keywords')}")
            print(f"Active: {offer.get('active')}")
            print(f"Product ID: {offer.get('product_id')}")
            
            # Check product data
            product_id = offer.get('product_id')
            if product_id:
                product_doc = db.collection("products").document(product_id).get()
                if product_doc.exists:
                    product = product_doc.to_dict()
                    print(f"Product Title: {product.get('title')}")
                    print(f"Product Categories: {product.get('categories')}")
                    print(f"Product Keywords: {product.get('keywords')}")
                    print(f"Has Embedding: {'embedding' in product}")
                else:
                    print("❌ Product not found!")
    
    # 3. Check for payment-related offers
    print("\n3. All offers with 'payment' in categories:")
    payment_offers = db.collection("offers").where("categories", "array_contains", "payments").stream()
    payment_count = 0
    for doc in payment_offers:
        payment_count += 1
        offer = doc.to_dict()
        print(f"   - {offer.get('title')}: active={offer.get('active')}")
    
    print(f"Offers with 'payments' category: {payment_count}")
    
    # 4. Check alternative category names
    print("\n4. Checking alternative category names:")
    alt_categories = ["payment", "fintech", "financial", "finance"]
    for cat in alt_categories:
        offers = db.collection("offers").where("categories", "array_contains", cat).stream()
        count = 0
        for doc in offers:
            count += 1
            offer = doc.to_dict()
            if "stripe" in offer.get("title", "").lower():
                print(f"   - Found Stripe in '{cat}' category: {offer.get('title')}")
        if count > 0:
            print(f"   - Category '{cat}': {count} offers")

if __name__ == "__main__":
    debug_stripe_offers()
