#!/usr/bin/env python3
"""
Test script to verify ACID properties in wallet transactions.

This script tests:
1. Atomicity: All operations in a transaction succeed or fail together
2. Consistency: Database remains in valid state after transactions
3. Isolation: Concurrent transactions don't interfere with each other
4. Durability: Committed transactions persist even after failures

Usage:
    python test_acid_wallet_transactions.py
"""

import sys
import os
import logging
import threading
import time
from typing import Dict, Any
import uuid
import concurrent.futures

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class ACIDWalletTester:
    """Test suite for ACID properties in wallet transactions."""
    
    def __init__(self):
        self.test_brand_id = "test_brand_acid"
        self.test_product_id = "test_product_acid"
        self.test_offers = []
        self.initial_wallet_balance = 1000.0  # $1000 initial balance
        
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete test offers
            for offer_id in self.test_offers:
                db.collection("offers").document(offer_id).delete()
            
            # Delete test product
            db.collection("products").document(self.test_product_id).delete()
            
            # Delete test wallet and transactions
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            
            # Delete all transactions
            transactions = wallet_ref.collection("transactions").stream()
            for tx in transactions:
                tx.reference.delete()
            
            # Delete wallet
            wallet_ref.delete()
            
            # Delete test brand
            db.collection("brands").document(self.test_brand_id).delete()
            
        except Exception as e:
            logger.debug(f"Cleanup error (expected): {e}")
        
        logger.info("✅ Cleanup completed")
    
    def setup_test_data(self):
        """Set up test brand, product, and wallet."""
        logger.info("🔧 Setting up test data...")
        
        # Create test brand
        brand_data = {
            "brand_id": self.test_brand_id,
            "company_name": "Test ACID Company",
            "website": "https://test-acid.com",
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("brands").document(self.test_brand_id).set(brand_data)
        
        # Create test product
        product_data = {
            "id": self.test_product_id,
            "title": "Test ACID Product",
            "description": "A test product for ACID testing",
            "brand_id": self.test_brand_id,
            "categories": ["testing"],
            "keywords": ["test", "acid"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("products").document(self.test_product_id).set(product_data)
        
        # Create test wallet with initial balance
        wallet_data = {
            "brand_id": self.test_brand_id,
            "total_available_balance": self.initial_wallet_balance,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("wallets").document(self.test_brand_id).set(wallet_data)
        
        logger.info(f"✅ Test data setup complete. Initial wallet balance: ${self.initial_wallet_balance}")
    
    def create_test_offer(self, budget_amount: float) -> str:
        """Create a test offer with budget allocation."""
        offer_id = str(uuid.uuid4())
        self.test_offers.append(offer_id)
        
        # Create offer
        offer_data = {
            "id": offer_id,
            "brand_id": self.test_brand_id,
            "product_id": self.test_product_id,
            "offer_title": f"Test ACID Offer {len(self.test_offers)}",
            "offer_description": "Test offer for ACID testing",
            "active": True,
            "status": "Active",
            "offer_total_budget_allocated": budget_amount,
            "offer_total_budget_spent": 0.0,
            "offer_total_promo_spent": 0.0,
            "remaining_budget": budget_amount,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("offers").document(offer_id).set(offer_data)
        
        # Update wallet to allocate budget using simple update (for testing)
        # In production, this would use the ACID transaction from the API
        wallet_ref = db.collection("wallets").document(self.test_brand_id)
        wallet_doc = wallet_ref.get()

        if not wallet_doc.exists:
            raise Exception("Wallet not found")

        wallet_data = wallet_doc.to_dict()
        current_available = wallet_data.get("total_available_balance", 0.0)
        current_allocated = wallet_data.get("total_budget_allocated", 0.0)

        if current_available < budget_amount:
            raise Exception(f"Insufficient balance: {current_available} < {budget_amount}")

        new_available = current_available - budget_amount
        new_allocated = current_allocated + budget_amount

        wallet_ref.update({
            "total_available_balance": new_available,
            "total_budget_allocated": new_allocated,
            "updated_at": firestore.SERVER_TIMESTAMP
        })

        balance_after = new_available
        
        # Create transaction record
        from api.routes.brands import create_wallet_transaction
        create_wallet_transaction(
            brand_id=self.test_brand_id,
            transaction_type="debit",
            category="budget_allocation",
            amount=budget_amount,
            description=f"Budget allocated for test offer: {offer_data['offer_title']}",
            balance_after=balance_after,
            reference_id=offer_id,
            reference_type="offer"
        )
        
        return offer_id
    
    def get_wallet_balance(self) -> Dict[str, float]:
        """Get current wallet balance."""
        wallet_doc = db.collection("wallets").document(self.test_brand_id).get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            return {
                "available": wallet_data.get("total_available_balance", 0.0),
                "allocated": wallet_data.get("total_budget_allocated", 0.0),
                "spent": wallet_data.get("total_balance_spent", 0.0)
            }
        return {"available": 0.0, "allocated": 0.0, "spent": 0.0}
    
    def test_atomicity(self) -> bool:
        """Test that transactions are atomic (all or nothing)."""
        logger.info("🧪 Testing Atomicity...")
        
        try:
            # Create an offer with valid budget
            offer_id = self.create_test_offer(100.0)
            balance_before = self.get_wallet_balance()
            
            # Try to create another offer with insufficient budget (should fail atomically)
            try:
                self.create_test_offer(2000.0)  # More than available balance
                logger.error("   ❌ Transaction should have failed due to insufficient balance")
                return False
            except Exception as e:
                logger.info(f"   ✅ Transaction correctly failed: {str(e)}")
            
            # Verify wallet state is unchanged after failed transaction
            balance_after = self.get_wallet_balance()
            
            if (balance_before["available"] == balance_after["available"] and 
                balance_before["allocated"] == balance_after["allocated"]):
                logger.info("   ✅ Wallet state unchanged after failed transaction (atomicity preserved)")
                return True
            else:
                logger.error("   ❌ Wallet state changed after failed transaction (atomicity violated)")
                return False
                
        except Exception as e:
            logger.error(f"❌ Atomicity test failed: {e}")
            return False
    
    def test_consistency(self) -> bool:
        """Test that database remains consistent after transactions."""
        logger.info("🧪 Testing Consistency...")
        
        try:
            # Create multiple offers and verify total balance consistency
            initial_balance = self.get_wallet_balance()
            
            offer_budgets = [50.0, 75.0, 100.0]
            created_offers = []
            
            for budget in offer_budgets:
                offer_id = self.create_test_offer(budget)
                created_offers.append(offer_id)
            
            final_balance = self.get_wallet_balance()
            total_allocated = sum(offer_budgets)
            
            # Verify consistency: available + allocated should equal initial total
            expected_available = initial_balance["available"] - total_allocated
            expected_allocated = initial_balance["allocated"] + total_allocated
            
            if (abs(final_balance["available"] - expected_available) < 0.01 and
                abs(final_balance["allocated"] - expected_allocated) < 0.01):
                logger.info(f"   ✅ Balance consistency maintained: allocated ${total_allocated}")
                
                # Verify total balance conservation
                initial_total = initial_balance["available"] + initial_balance["allocated"]
                final_total = final_balance["available"] + final_balance["allocated"]
                
                if abs(initial_total - final_total) < 0.01:
                    logger.info("   ✅ Total balance conserved (consistency preserved)")
                    return True
                else:
                    logger.error(f"   ❌ Total balance not conserved: {initial_total} != {final_total}")
                    return False
            else:
                logger.error("   ❌ Balance consistency violated")
                return False
                
        except Exception as e:
            logger.error(f"❌ Consistency test failed: {e}")
            return False
    
    def test_isolation(self) -> bool:
        """Test that concurrent transactions don't interfere with each other."""
        logger.info("🧪 Testing Isolation...")
        
        try:
            # Create multiple concurrent transactions
            def concurrent_offer_creation(budget: float, thread_id: int):
                try:
                    offer_id = self.create_test_offer(budget)
                    logger.info(f"   Thread {thread_id}: Created offer {offer_id} with budget ${budget}")
                    return {"success": True, "offer_id": offer_id, "budget": budget}
                except Exception as e:
                    logger.info(f"   Thread {thread_id}: Failed to create offer with budget ${budget}: {str(e)}")
                    return {"success": False, "error": str(e), "budget": budget}
            
            # Run concurrent transactions
            budgets = [50.0, 60.0, 70.0, 80.0, 90.0]
            results = []
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [
                    executor.submit(concurrent_offer_creation, budget, i) 
                    for i, budget in enumerate(budgets)
                ]
                
                for future in concurrent.futures.as_completed(futures):
                    results.append(future.result())
            
            # Analyze results
            successful_creations = [r for r in results if r["success"]]
            failed_creations = [r for r in results if not r["success"]]
            
            total_successful_budget = sum(r["budget"] for r in successful_creations)
            
            # Verify final wallet state
            final_balance = self.get_wallet_balance()
            
            logger.info(f"   Successful creations: {len(successful_creations)}")
            logger.info(f"   Failed creations: {len(failed_creations)}")
            logger.info(f"   Total successful budget: ${total_successful_budget}")
            logger.info(f"   Final allocated balance: ${final_balance['allocated']}")
            
            # Check if the final state is consistent with successful transactions
            if abs(final_balance["allocated"] - total_successful_budget) < 0.01:
                logger.info("   ✅ Concurrent transactions isolated correctly")
                return True
            else:
                logger.error("   ❌ Concurrent transactions interfered with each other")
                return False
                
        except Exception as e:
            logger.error(f"❌ Isolation test failed: {e}")
            return False
    
    def test_durability(self) -> bool:
        """Test that committed transactions persist."""
        logger.info("🧪 Testing Durability...")
        
        try:
            # Create an offer and verify it persists
            balance_before = self.get_wallet_balance()
            offer_id = self.create_test_offer(150.0)
            
            # Simulate a brief delay (as if system restarted)
            time.sleep(0.1)
            
            # Re-read the data to verify persistence
            balance_after = self.get_wallet_balance()
            offer_doc = db.collection("offers").document(offer_id).get()
            
            if offer_doc.exists:
                offer_data = offer_doc.to_dict()
                expected_allocated = balance_before["allocated"] + 150.0
                
                if (abs(balance_after["allocated"] - expected_allocated) < 0.01 and
                    offer_data.get("offer_total_budget_allocated") == 150.0):
                    logger.info("   ✅ Transaction persisted correctly (durability preserved)")
                    return True
                else:
                    logger.error("   ❌ Transaction data inconsistent after persistence")
                    return False
            else:
                logger.error("   ❌ Offer document not found after transaction")
                return False
                
        except Exception as e:
            logger.error(f"❌ Durability test failed: {e}")
            return False
    
    def run_acid_tests(self) -> bool:
        """Run all ACID property tests."""
        logger.info("🚀 Starting ACID properties tests...")
        
        try:
            # Setup
            self.cleanup()
            self.setup_test_data()
            
            tests = [
                ("Atomicity", self.test_atomicity),
                ("Consistency", self.test_consistency),
                ("Isolation", self.test_isolation),
                ("Durability", self.test_durability)
            ]
            
            passed = 0
            total = len(tests)
            
            for test_name, test_func in tests:
                logger.info(f"📋 Running ACID test: {test_name}")
                if test_func():
                    passed += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                
                # Brief pause between tests
                time.sleep(0.1)
            
            # Final results
            final_balance = self.get_wallet_balance()
            
            logger.info("=" * 60)
            logger.info("📊 ACID TESTS RESULTS")
            logger.info("=" * 60)
            logger.info(f"Tests passed: {passed}/{total}")
            logger.info(f"Initial wallet balance: ${self.initial_wallet_balance}")
            logger.info(f"Final available balance: ${final_balance['available']}")
            logger.info(f"Final allocated balance: ${final_balance['allocated']}")
            logger.info(f"Total offers created: {len(self.test_offers)}")
            logger.info("=" * 60)
            
            if passed == total:
                logger.info("🎉 All ACID properties tests PASSED!")
                return True
            else:
                logger.error(f"💥 {total - passed} ACID tests FAILED!")
                return False
                
        except Exception as e:
            logger.error(f"💥 ACID test suite failed: {e}")
            return False
        finally:
            # Cleanup
            self.cleanup()


def main():
    """Main function to run the ACID tests."""
    tester = ACIDWalletTester()
    
    try:
        success = tester.run_acid_tests()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
