#!/usr/bin/env python3
"""
Test script to verify the complete offer cancel/reinstate wallet flow.

This script tests:
1. Create offer with budget allocation
2. Cancel offer and verify wallet refund
3. Reinstate offer and verify wallet deduction
4. Verify all transaction records are correct

Usage:
    python test_offer_wallet_flow.py
"""

import sys
import os
import logging
from typing import Dict, Any
import uuid

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from firebase.config import get_db
from firebase_admin import firestore

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

db = get_db()


class OfferWalletFlowTester:
    """Test suite for offer cancel/reinstate wallet operations."""
    
    def __init__(self):
        self.test_brand_id = "test_brand_wallet_flow"
        self.test_product_id = "test_product_wallet_flow"
        self.test_offer_id = None
        self.initial_wallet_balance = 100.0  # $100 initial balance
        self.test_budget_amount = 25.0  # $25 test budget
        
    def cleanup(self):
        """Clean up test data."""
        logger.info("🧹 Cleaning up test data...")
        
        try:
            # Delete test offer
            if self.test_offer_id:
                db.collection("offers").document(self.test_offer_id).delete()
            
            # Delete test product
            db.collection("products").document(self.test_product_id).delete()
            
            # Delete test wallet and transactions
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            
            # Delete all transactions
            transactions = wallet_ref.collection("transactions").stream()
            for tx in transactions:
                tx.reference.delete()
            
            # Delete wallet
            wallet_ref.delete()
            
            # Delete test brand
            db.collection("brands").document(self.test_brand_id).delete()
            
        except Exception as e:
            logger.debug(f"Cleanup error (expected): {e}")
        
        logger.info("✅ Cleanup completed")
    
    def setup_test_data(self):
        """Set up test brand, product, and wallet."""
        logger.info("🔧 Setting up test data...")
        
        # Create test brand
        brand_data = {
            "brand_id": self.test_brand_id,
            "company_name": "Test Wallet Flow Company",
            "website": "https://test-wallet-flow.com",
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("brands").document(self.test_brand_id).set(brand_data)
        
        # Create test product
        product_data = {
            "id": self.test_product_id,
            "title": "Test Wallet Flow Product",
            "description": "A test product for wallet flow testing",
            "brand_id": self.test_brand_id,
            "categories": ["testing"],
            "keywords": ["test", "wallet", "flow"],
            "created_at": firestore.SERVER_TIMESTAMP,
            "active_offers": [],
            "inactive_offers": []
        }
        db.collection("products").document(self.test_product_id).set(product_data)
        
        # Create test wallet with initial balance
        wallet_data = {
            "brand_id": self.test_brand_id,
            "total_available_balance": self.initial_wallet_balance,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("wallets").document(self.test_brand_id).set(wallet_data)
        
        logger.info(f"✅ Test data setup complete. Initial wallet balance: ${self.initial_wallet_balance}")
    
    def create_test_offer(self) -> str:
        """Create a test offer with budget allocation."""
        logger.info(f"📝 Creating test offer with ${self.test_budget_amount} budget...")
        
        self.test_offer_id = str(uuid.uuid4())
        
        # Create offer
        offer_data = {
            "id": self.test_offer_id,
            "brand_id": self.test_brand_id,
            "product_id": self.test_product_id,
            "offer_title": "Test Wallet Flow Offer",
            "offer_description": "Test offer for wallet flow testing",
            "active": True,
            "status": "Active",
            "offer_total_budget_allocated": self.test_budget_amount,
            "offer_total_budget_spent": 0.0,
            "offer_total_promo_spent": 0.0,
            "remaining_budget": self.test_budget_amount,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        db.collection("offers").document(self.test_offer_id).set(offer_data)
        
        # Update wallet to allocate budget
        wallet_ref = db.collection("wallets").document(self.test_brand_id)
        wallet_ref.update({
            "total_available_balance": firestore.Increment(-self.test_budget_amount),
            "total_budget_allocated": firestore.Increment(self.test_budget_amount),
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        
        # Create transaction record
        from api.routes.brands import create_wallet_transaction
        create_wallet_transaction(
            brand_id=self.test_brand_id,
            transaction_type="debit",
            category="budget_allocation",
            amount=self.test_budget_amount,
            description=f"Budget allocated for test offer: {offer_data['offer_title']}",
            balance_after=self.initial_wallet_balance - self.test_budget_amount,
            reference_id=self.test_offer_id,
            reference_type="offer"
        )
        
        logger.info(f"✅ Test offer created: {self.test_offer_id}")
        return self.test_offer_id
    
    def get_wallet_balance(self) -> Dict[str, float]:
        """Get current wallet balance."""
        wallet_doc = db.collection("wallets").document(self.test_brand_id).get()
        if wallet_doc.exists:
            wallet_data = wallet_doc.to_dict()
            return {
                "available": wallet_data.get("total_available_balance", 0.0),
                "allocated": wallet_data.get("total_budget_allocated", 0.0),
                "spent": wallet_data.get("total_balance_spent", 0.0)
            }
        return {"available": 0.0, "allocated": 0.0, "spent": 0.0}
    
    def get_transaction_count(self) -> int:
        """Get number of wallet transactions."""
        transactions = db.collection("wallets").document(self.test_brand_id).collection("transactions").stream()
        return len(list(transactions))
    
    def test_cancel_offer(self) -> bool:
        """Test canceling an offer and verify wallet refund."""
        logger.info("🧪 Testing offer cancellation...")
        
        try:
            # Get wallet balance before cancellation
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            
            logger.info(f"   Before cancel - Available: ${balance_before['available']}, Allocated: ${balance_before['allocated']}")
            
            # Simulate offer cancellation logic
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_doc = offer_ref.get()
            offer_data = offer_doc.to_dict()
            
            # Calculate remaining budget
            total_allocated = offer_data.get("offer_total_budget_allocated", 0.0)
            total_spent = offer_data.get("offer_total_budget_spent", 0.0)
            remaining_budget = total_allocated - total_spent
            
            # Cancel the offer
            offer_ref.update({
                "active": False,
                "status": "Cancelled",
                "cancelled_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Refund remaining budget to wallet
            if remaining_budget > 0:
                wallet_ref = db.collection("wallets").document(self.test_brand_id)
                wallet_doc = wallet_ref.get()
                wallet_data = wallet_doc.to_dict()
                current_balance = wallet_data.get("total_available_balance", 0.0)
                new_balance = current_balance + remaining_budget
                
                wallet_ref.update({
                    "total_available_balance": firestore.Increment(remaining_budget),
                    "total_budget_allocated": firestore.Increment(-remaining_budget),
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                
                # Create transaction record
                from api.routes.brands import create_wallet_transaction
                create_wallet_transaction(
                    brand_id=self.test_brand_id,
                    transaction_type="credit",
                    category="budget_refund",
                    amount=remaining_budget,
                    description=f"Budget refund from cancelled offer: {offer_data.get('offer_title', 'Untitled')}",
                    balance_after=new_balance,
                    reference_id=self.test_offer_id,
                    reference_type="offer"
                )
            
            # Verify wallet balance after cancellation
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            
            logger.info(f"   After cancel - Available: ${balance_after['available']}, Allocated: ${balance_after['allocated']}")
            logger.info(f"   Refunded amount: ${remaining_budget}")
            
            # Verify the changes
            expected_available = balance_before['available'] + remaining_budget
            expected_allocated = balance_before['allocated'] - remaining_budget
            
            if abs(balance_after['available'] - expected_available) < 0.01:
                logger.info("   ✅ Available balance correctly increased")
            else:
                logger.error(f"   ❌ Available balance incorrect. Expected: ${expected_available}, Got: ${balance_after['available']}")
                return False
            
            if abs(balance_after['allocated'] - expected_allocated) < 0.01:
                logger.info("   ✅ Allocated balance correctly decreased")
            else:
                logger.error(f"   ❌ Allocated balance incorrect. Expected: ${expected_allocated}, Got: ${balance_after['allocated']}")
                return False
            
            if tx_count_after == tx_count_before + 1:
                logger.info("   ✅ Transaction record created")
            else:
                logger.error(f"   ❌ Transaction count incorrect. Expected: {tx_count_before + 1}, Got: {tx_count_after}")
                return False
            
            logger.info("✅ Offer cancellation test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Offer cancellation test failed: {e}")
            return False
    
    def test_reinstate_offer(self) -> bool:
        """Test reinstating an offer and verify wallet deduction."""
        logger.info("🧪 Testing offer reinstatement...")
        
        try:
            # Get wallet balance before reinstatement
            balance_before = self.get_wallet_balance()
            tx_count_before = self.get_transaction_count()
            
            reinstate_budget = 30.0  # $30 for reinstatement
            
            logger.info(f"   Before reinstate - Available: ${balance_before['available']}, Allocated: ${balance_before['allocated']}")
            logger.info(f"   Reinstating with budget: ${reinstate_budget}")
            
            # Check if we have sufficient balance
            if balance_before['available'] < reinstate_budget:
                logger.error(f"   ❌ Insufficient balance for reinstatement")
                return False
            
            # Simulate offer reinstatement logic
            offer_ref = db.collection("offers").document(self.test_offer_id)
            offer_doc = offer_ref.get()
            offer_data = offer_doc.to_dict()
            
            # Reinstate the offer
            offer_ref.update({
                "active": True,
                "status": "Active",
                "offer_total_budget_allocated": reinstate_budget,
                "offer_total_budget_spent": 0.0,
                "offer_total_promo_spent": 0.0,
                "remaining_budget": reinstate_budget,
                "reactivated_at": firestore.SERVER_TIMESTAMP,
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Deduct budget from wallet
            wallet_ref = db.collection("wallets").document(self.test_brand_id)
            wallet_ref.update({
                "total_available_balance": firestore.Increment(-reinstate_budget),
                "total_budget_allocated": firestore.Increment(reinstate_budget),
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            
            # Create transaction record
            from api.routes.brands import create_wallet_transaction
            create_wallet_transaction(
                brand_id=self.test_brand_id,
                transaction_type="debit",
                category="budget_allocation",
                amount=reinstate_budget,
                description=f"Budget allocated for reinstated offer: {offer_data.get('offer_title', 'Untitled')}",
                balance_after=balance_before['available'] - reinstate_budget,
                reference_id=self.test_offer_id,
                reference_type="offer"
            )
            
            # Verify wallet balance after reinstatement
            balance_after = self.get_wallet_balance()
            tx_count_after = self.get_transaction_count()
            
            logger.info(f"   After reinstate - Available: ${balance_after['available']}, Allocated: ${balance_after['allocated']}")
            
            # Verify the changes
            expected_available = balance_before['available'] - reinstate_budget
            expected_allocated = balance_before['allocated'] + reinstate_budget
            
            if abs(balance_after['available'] - expected_available) < 0.01:
                logger.info("   ✅ Available balance correctly decreased")
            else:
                logger.error(f"   ❌ Available balance incorrect. Expected: ${expected_available}, Got: ${balance_after['available']}")
                return False
            
            if abs(balance_after['allocated'] - expected_allocated) < 0.01:
                logger.info("   ✅ Allocated balance correctly increased")
            else:
                logger.error(f"   ❌ Allocated balance incorrect. Expected: ${expected_allocated}, Got: ${balance_after['allocated']}")
                return False
            
            if tx_count_after == tx_count_before + 1:
                logger.info("   ✅ Transaction record created")
            else:
                logger.error(f"   ❌ Transaction count incorrect. Expected: {tx_count_before + 1}, Got: {tx_count_after}")
                return False
            
            logger.info("✅ Offer reinstatement test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Offer reinstatement test failed: {e}")
            return False
    
    def run_complete_flow_test(self) -> bool:
        """Run the complete offer wallet flow test."""
        logger.info("🚀 Starting complete offer wallet flow test...")
        
        try:
            # Setup
            self.cleanup()
            self.setup_test_data()
            
            # Test 1: Create offer
            logger.info("📋 Step 1: Creating test offer...")
            self.create_test_offer()
            
            initial_balance = self.get_wallet_balance()
            expected_available = self.initial_wallet_balance - self.test_budget_amount
            expected_allocated = self.test_budget_amount
            
            if abs(initial_balance['available'] - expected_available) < 0.01 and abs(initial_balance['allocated'] - expected_allocated) < 0.01:
                logger.info("✅ Step 1 passed: Offer creation and budget allocation")
            else:
                logger.error("❌ Step 1 failed: Incorrect wallet balance after offer creation")
                return False
            
            # Test 2: Cancel offer
            logger.info("📋 Step 2: Testing offer cancellation...")
            if not self.test_cancel_offer():
                return False
            
            # Test 3: Reinstate offer
            logger.info("📋 Step 3: Testing offer reinstatement...")
            if not self.test_reinstate_offer():
                return False
            
            # Final verification
            final_balance = self.get_wallet_balance()
            final_tx_count = self.get_transaction_count()
            
            logger.info("=" * 60)
            logger.info("📊 FINAL RESULTS")
            logger.info("=" * 60)
            logger.info(f"Initial wallet balance: ${self.initial_wallet_balance}")
            logger.info(f"Final available balance: ${final_balance['available']}")
            logger.info(f"Final allocated balance: ${final_balance['allocated']}")
            logger.info(f"Total transactions created: {final_tx_count}")
            logger.info("=" * 60)
            
            if final_tx_count == 3:  # Initial allocation + refund + reinstate allocation
                logger.info("🎉 Complete flow test PASSED!")
                return True
            else:
                logger.error("💥 Complete flow test FAILED!")
                return False
                
        except Exception as e:
            logger.error(f"💥 Complete flow test failed: {e}")
            return False
        finally:
            # Cleanup
            self.cleanup()


def main():
    """Main function to run the tests."""
    tester = OfferWalletFlowTester()
    
    try:
        success = tester.run_complete_flow_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"💥 Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
