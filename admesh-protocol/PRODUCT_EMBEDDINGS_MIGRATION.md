# Product Embeddings Migration Guide

## Overview

This document describes the migration of product embeddings from the `products` collection to a dedicated `product_embeddings` collection. This refactoring provides better performance, cleaner separation of concerns, and more efficient vector similarity searches.

## What Changed

### Before (Old Structure)
```
products/{productId}
├── id: string
├── title: string
├── description: string
├── categories: array
├── keywords: array
├── embedding: array[1536]          ← Stored within product document
├── embedding_updated_at: timestamp
└── ... other product fields
```

### After (New Structure)
```
products/{productId}
├── id: string
├── title: string
├── description: string
├── categories: array
├── keywords: array
└── ... other product fields (no embedding)

product_embeddings/{productId}      ← New dedicated collection
├── product_id: string
├── embedding: array[1536]
├── dimensions: number
├── model: string
├── source_text: string
├── generation_method: string
├── created_at: timestamp
├── updated_at: timestamp
└── ... metadata fields
```

## Benefits

1. **Better Performance**: Faster retrieval when only embedding data is needed
2. **Cleaner Separation**: Product data and embedding data are logically separated
3. **Efficient Indexing**: Better indexing for embedding-specific queries
4. **Metadata Support**: Rich metadata about embedding generation and updates
5. **Scalability**: Easier to implement vector database integration in the future

## Migration Process

### 1. Automatic Migration
The system includes automatic migration logic in the `get_product_embedding()` function:
- When an embedding is requested, it first checks the new `product_embeddings` collection
- If not found, it checks the old `products` collection
- If found in the old collection, it automatically migrates to the new collection
- This ensures zero-downtime migration

### 2. Manual Migration Script
For bulk migration, use the provided script:

```bash
# Dry run to see what would be migrated
python migrate_product_embeddings.py --dry-run

# Migrate all embeddings
python migrate_product_embeddings.py

# Migrate and remove from products collection
python migrate_product_embeddings.py --remove-from-products

# Custom batch size
python migrate_product_embeddings.py --batch-size=100
```

### 3. Testing
Verify the migration with the test script:

```bash
python test_product_embeddings_refactor.py
```

## Code Changes

### New Utility Functions
- `get_product_embedding_from_collection()` - Retrieve from new collection
- `store_product_embedding_in_collection()` - Store in new collection
- `update_product_embedding_in_collection()` - Update in new collection
- `delete_product_embedding_from_collection()` - Delete from new collection
- `generate_embedding_text_from_product()` - Generate embedding text
- `get_or_create_product_embedding_new()` - New version for future use

### Updated Files
- `api/utils/embedding.py` - New utility functions and transitional logic
- `api/routes/products.py` - Product creation/update uses new collection
- `api/routes/offers.py` - Offer tracking uses new collection
- `api/routes/brands.py` - Brand onboarding uses new collection
- `api/routes/agent_recommendation.py` - Imports updated function
- `fix_stripe_categories.py` - Updated to use new collection

### Transitional Function
The `get_product_embedding()` function now:
1. Checks new collection first
2. Falls back to old collection if needed
3. Automatically migrates old embeddings
4. Maintains backward compatibility

## Deployment Strategy

### Phase 1: Deploy New Code (✅ Complete)
- Deploy the updated code with transitional logic
- All new embeddings go to the new collection
- Old embeddings are automatically migrated on access
- Zero downtime, backward compatible

### Phase 2: Run Migration Script (Optional)
- Run the migration script to bulk migrate remaining embeddings
- Can be done during low-traffic periods
- Use `--dry-run` first to verify

### Phase 3: Monitor and Verify
- Monitor logs for migration activity
- Run test script to verify functionality
- Check that new embeddings are being created in the new collection

### Phase 4: Cleanup (Future)
- After confirming all embeddings are migrated, optionally remove embedding fields from products collection
- Update code to use `get_or_create_product_embedding_new()` function
- Remove transitional logic

## Monitoring

### Key Metrics to Watch
- Embedding retrieval performance
- Migration success rate
- New embedding creation rate
- Error rates in embedding operations

### Log Messages to Monitor
- `✅ Found embedding in product_embeddings collection` - New collection hit
- `✅ Successfully migrated embedding` - Automatic migration
- `💾 Generated and stored new embedding` - New embedding creation
- `❌ Failed to store embedding` - Errors to investigate

## Rollback Plan

If issues arise, the system can be rolled back:

1. **Code Rollback**: Revert to previous version that uses products collection
2. **Data Integrity**: Old embeddings remain in products collection
3. **New Embeddings**: May need to regenerate embeddings created in new collection

## Testing

### Automated Tests
Run the comprehensive test suite:
```bash
python test_product_embeddings_refactor.py
```

### Manual Testing
1. Create a new product - verify embedding goes to new collection
2. Update a product - verify embedding is updated in new collection
3. Run recommendation API - verify it works with new collection
4. Check migration of old embeddings

## Performance Considerations

### Expected Improvements
- Faster embedding retrieval (smaller documents)
- Better query performance for similarity searches
- Reduced memory usage when loading product data

### Potential Impacts
- Slight increase in Firestore read operations (separate collection access)
- Additional storage for metadata (minimal impact)

## Future Enhancements

With the new structure, we can easily:
1. Implement vector database integration (Pinecone, Weaviate, etc.)
2. Add embedding versioning and A/B testing
3. Implement more sophisticated embedding strategies
4. Add embedding analytics and monitoring

## Support

For issues or questions:
1. Check the logs for error messages
2. Run the test script to verify functionality
3. Use the migration script's dry-run mode to diagnose issues
4. Review this documentation for troubleshooting steps
